"use client";

import { useEffect, useState } from "react";
import * as THREE from "three";

export function SelectionOutline({ selectedObjects, children }) {
  const [outlineData, setOutlineData] = useState([]);

  useEffect(() => {
    console.log("SelectionOutline - selectedObjects:", selectedObjects);
    console.log(
      "SelectionOutline - selectedObjects length:",
      selectedObjects?.length
    );

    if (!selectedObjects || selectedObjects.length === 0) {
      setOutlineData([]);
      return;
    }

    const outlines = selectedObjects
      .map((object, index) => {
        if (object && object.isMesh && object.geometry) {
          console.log(`Creating outline for object ${index}:`, object.name);

          // Get world transform
          object.updateMatrixWorld();
          const position = new THREE.Vector3();
          const quaternion = new THREE.Quaternion();
          const scale = new THREE.Vector3();
          object.matrixWorld.decompose(position, quaternion, scale);

          // Create edges geometry for thin outline
          const edges = new THREE.EdgesGeometry(object.geometry, 30); // 30 degree threshold

          return {
            key: `outline-${object.uuid}-${index}`,
            edgesGeometry: edges,
            position: position.toArray(),
            quaternion: quaternion.toArray(),
            scale: scale.toArray(),
            name: object.name,
          };
        }
        return null;
      })
      .filter(Boolean);

    setOutlineData(outlines);
  }, [selectedObjects]);

  return (
    <>
      {children}
      {outlineData.map((outline) => (
        <lineSegments
          key={outline.key}
          geometry={outline.edgesGeometry}
          position={outline.position}
          quaternion={outline.quaternion}
          scale={outline.scale}
          renderOrder={1000}
        >
          <lineBasicMaterial
            color="orange"
            transparent
            opacity={1}
            linewidth={2}
            depthTest={false}
          />
        </lineSegments>
      ))}
    </>
  );
}
