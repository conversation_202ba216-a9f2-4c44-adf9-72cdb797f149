import React, { useEffect } from "react";
import { useGLTF } from "@react-three/drei";

export function Jewelry01({ position, wireframe, onLoad, onClick }) {
  const { scene } = useGLTF("/models/01.glb");

  useEffect(() => {
    scene.traverse((child) => {
      if (child.isMesh) {
        child.material.wireframe = wireframe;
        child.onClick = onClick;
      }
    });
    onLoad?.(scene);
  }, [scene, wireframe, onLoad, onClick]);

  return <primitive object={scene} position={position} />;
}

useGLTF.preload("/models/01.glb");
