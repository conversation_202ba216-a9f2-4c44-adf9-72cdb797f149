"use client";

import { useState, useRef, useEffect, useCallback, Suspense } from "react";
import { Canvas, useThree } from "@react-three/fiber";
import {
  OrbitControls,
  GizmoHelper,
  GizmoViewport,
  TransformControls,
  MeshReflectorMaterial,
} from "@react-three/drei";
import { ToastContainer } from "react-toastify";

import SceneLights from "./SceneLights";
import { Environment } from "./Environment";
import { MaterialPanel } from "./MaterialPanel";
import { TransformPanel } from "./TransformPanel";
import { LightPanel } from "./LightPanel";
import { PostProcessingPanel } from "./PostProcessingPanel";
import { EnvironmentPanel } from "./EnvironmentPanel";
import { ModelStats } from "./ModelStats";
import * as THREE from "three";
import Header from "./Header";
import { Outliner } from "./Outliner";
import Toolbar from "./Toolbar";
import { SceneSwitcher } from "./SceneSwitcher";
import { predefinedAnimations } from "./animations/CameraAnimations";
import { ModelStage } from "./ModelStage";
import { PostProcessingEffects } from "./PostProcessingEffects";
import { EngravingPanel } from "./EngravingPanel";
import { SelectionOutline } from "./SelectionOutline";

function calculateModelStats(scene) {
  let vertexCount = 0;
  let triangleCount = 0;
  const materials = new Set();

  scene.traverse((object) => {
    if (object.isMesh) {
      const geometry = object.geometry;
      vertexCount += geometry.attributes.position.count;
      triangleCount += geometry.index
        ? geometry.index.count / 3
        : geometry.attributes.position.count / 3;
      materials.add(object.material);
    }
  });

  return {
    vertices: vertexCount,
    triangles: triangleCount,
    materials: materials.size,
    animations: scene.animations?.length || 0,
  };
}

const setShadowsOnModel = (scene) => {
  scene.traverse((child) => {
    if (child.isMesh) {
      child.castShadow = true;
      child.receiveShadow = true;
    }
  });
};

const ScreenshotTaker = ({ onScreenshotRef }) => {
  const { gl, scene, camera } = useThree();

  useEffect(() => {
    if (typeof onScreenshotRef === "function") {
      onScreenshotRef(() => {
        try {
          // Render the scene
          gl.render(scene, camera);

          // Get the canvas data URL
          const dataURL = gl.domElement.toDataURL("image/png");

          // Create and trigger a download
          const link = document.createElement("a");
          const timestamp = new Date()
            .toISOString()
            .replace(/:/g, "-")
            .substring(0, 19);
          link.download = `agape-scene-${timestamp}.png`;
          link.href = dataURL;
          document.body.appendChild(link);
          link.click();

          // Clean up
          setTimeout(() => {
            document.body.removeChild(link);
          }, 100);

          console.log("Screenshot captured successfully");
          return true;
        } catch (error) {
          console.error("Error capturing screenshot:", error);
          return false;
        }
      });
    }
  }, [gl, scene, camera, onScreenshotRef]);

  return null;
};

export default function Experience() {
  const configRef = useRef(null);
  const [selectedModel, setSelectedModel] = useState("chosenring");
  const [uploadedModel, setUploadedModel] = useState(null);
  const [modelStats, setModelStats] = useState({
    vertices: 0,
    triangles: 0,
    materials: 0,
    animations: 0,
  });
  const [lights, setLights] = useState([
    {
      id: "defaultDirectional",
      type: "directional",
      position: [2.5, 5.5, 5],
      intensity: 1.0,
      color: "#ffffff",
      name: "Main Directional Light",
      castShadow: true,
      helperVisible: false,
    },
    {
      id: "keyLight",
      type: "point",
      position: [5, 5, 5],
      intensity: 8.0,
      color: "#ffffff",
      name: "Key Light",
    },
    {
      id: "fillLight",
      type: "point",
      position: [-5, 5, -5],
      intensity: 4.0,
      color: "#ffffff",
      name: "Fill Light",
    },
    {
      id: "backLight",
      type: "point",
      position: [0, 5, -5],
      intensity: 2.0,
      color: "#ffffff",
      name: "Back Light",
    },
  ]);
  const lightRefs = useRef({});
  const [selectedObjects, setSelectedObjects] = useState([]);
  const [selectedLight, setSelectedLight] = useState(null);
  const [lightPositionHistory, setLightPositionHistory] = useState({});
  const [sceneObjects, setSceneObjects] = useState([]);
  const [transformMode, setTransformMode] = useState("translate");
  const selectionGroupRef = useRef(new THREE.Group());

  const [rightPanelTab, setRightPanelTab] = useState(null);

  // Decal placement state
  const [isPlacingDecal, setIsPlacingDecal] = useState(false);
  const [showDecalDebug, setShowDecalDebug] = useState(false);

  // Scene controls state
  const [showGrid, setShowGrid] = useState(true);
  const [wireframe, setWireframe] = useState(false);
  const [groundType, setGroundType] = useState("backplate");
  const [envPreset, setEnvPreset] = useState("sunset");
  const [bgColor, setBgColor] = useState("#363643");
  const [showEnvironment, setShowEnvironment] = useState(false);
  const [showLightSpheres, setShowLightSpheres] = useState(true);
  const [customHdri, setCustomHdri] = useState(null);
  const [envIntensity, setEnvIntensity] = useState(1.5);
  const [envBlur, setEnvBlur] = useState(0.35);
  const [envRotation, setEnvRotation] = useState(0);

  const [postProcessingEnabled, setPostProcessingEnabled] = useState(true);
  const [postProcessingSettings, setPostProcessingSettings] = useState({
    autofocus: {
      enabled: false,
      bokehScale: 18,
    },
    bloom: {
      enabled: true,
      intensity: 0.07,
      threshold: 0.8,
      radius: 0.4,
    },
    dof: {
      enabled: false,
      focusDistance: 0,
      aperture: 0.01,
      bokehScale: 3,
    },
    vignette: {
      enabled: true,
      darkness: 0.5,
      offset: 0.1,
    },
  });

  // Store initial state
  const initialStateRef = useRef({
    selectedModel: null,
    lights: [],
    envPreset: null,
    bgColor: null,
    postProcessingSettings: null,
    showGrid: null,
    wireframe: null,
    groundType: null,
    showEnvironment: null,
    showLightSpheres: null,
    envIntensity: null,
    envBlur: null,
    cameraPosition: null,
    cameraTarget: null,
  });

  // Add a ref to store the initial state of model objects
  const modelInitialStateRef = useRef({
    objects: [],
  });

  // Capture initial state when component mounts
  useEffect(() => {
    initialStateRef.current = {
      selectedModel,
      lights: [...lights],
      envPreset,
      bgColor,
      postProcessingSettings: { ...postProcessingSettings },
      showGrid,
      wireframe,
      groundType,
      showEnvironment,
      showLightSpheres,
      envIntensity,
      envBlur,
      cameraPosition: new THREE.Vector3(5, 2, 5),
      cameraTarget: new THREE.Vector3(0, 1, 0),
    };
  }, []);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const modelParam = urlParams.get("model");
    if (modelParam === "jewelry03" || modelParam === "chosenring") {
      setEnvPreset("hdri_15");
    }
  }, []);

  const orbitControlsRef = useRef();
  const sceneRef = useRef();

  // History state for undo/redo
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isUndoRedoAction, setIsUndoRedoAction] = useState(false);

  const [showModelStats, setShowModelStats] = useState(false);

  // Add a new state to the history
  const addToHistory = useCallback(() => {
    if (isUndoRedoAction) {
      setIsUndoRedoAction(false);
      return;
    }
    const newState = getFullConfig();
    const newHistory = history.slice(0, historyIndex + 1);
    setHistory([...newHistory, newState]);
    setHistoryIndex(historyIndex + 1);
  }, [history, historyIndex, isUndoRedoAction, getFullConfig]);

  const pendingSelectedObjectsUUIDs = useRef(null);

  // Define forceUpdate and version setters early so they can be used in restoreConfig
  const [_, setForceUpdate] = useState(0);
  const forceUpdate = useCallback(() => setForceUpdate((f) => f + 1), []);
  const [transformVersion, setTransformVersion] = useState(0);
  const [materialVersion, setMaterialVersion] = useState(0);

  const restoreConfig = useCallback(
    (config) => {
      setSelectedModel(config.selectedModel);
      setUploadedModel(config.uploadedModel);
      setLights(config.lights);
      setEnvPreset(config.envPreset);
      setBgColor(config.bgColor);
      setShowEnvironment(config.showEnvironment);
      setShowLightSpheres(config.showLightSpheres);
      setCustomHdri(config.customHdri);
      setEnvIntensity(config.envIntensity);
      setEnvBlur(config.envBlur);
      setEnvRotation(config.envRotation);
      setPostProcessingEnabled(config.postProcessingEnabled);
      setPostProcessingSettings(config.postProcessingSettings);
      setShowGrid(config.showGrid);
      setWireframe(config.wireframe);
      setGroundType(config.groundType);

      // Restore object states (transforms and materials)
      if (config.objectStates && sceneObjects.length > 0) {
        config.objectStates.forEach((savedState) => {
          const obj = sceneObjects.find((o) => o.uuid === savedState.uuid);
          if (obj) {
            // Restore transform
            obj.position.fromArray(savedState.position);
            obj.rotation.fromArray(savedState.rotation);
            obj.scale.fromArray(savedState.scale);

            // Restore material properties
            if (savedState.material && obj.material) {
              const material = obj.material;
              if (material.color) {
                material.color.setHex(parseInt(savedState.material.color, 16));
              }
              if (savedState.material.roughness !== undefined) {
                material.roughness = savedState.material.roughness;
              }
              if (savedState.material.metalness !== undefined) {
                material.metalness = savedState.material.metalness;
              }
              if (savedState.material.clearcoat !== undefined) {
                material.clearcoat = savedState.material.clearcoat;
              }
              if (savedState.material.clearcoatRoughness !== undefined) {
                material.clearcoatRoughness =
                  savedState.material.clearcoatRoughness;
              }
              if (savedState.material.wireframe !== undefined) {
                material.wireframe = savedState.material.wireframe;
              }
              if (savedState.material.transparent !== undefined) {
                material.transparent = savedState.material.transparent;
              }
              if (savedState.material.opacity !== undefined) {
                material.opacity = savedState.material.opacity;
              }
              if (savedState.material.envMapIntensity !== undefined) {
                material.envMapIntensity = savedState.material.envMapIntensity;
              }
              material.needsUpdate = true;
            }
          }
        });
        // Force update to reflect changes
        forceUpdate();
        setMaterialVersion((v) => v + 1);
        setTransformVersion((v) => v + 1);
      }

      // Store selectedObjects UUIDs to be restored after sceneObjects update
      pendingSelectedObjectsUUIDs.current = config.selectedObjects || [];
      console.log("[restoreConfig] Restoring config", config);
    },
    [sceneObjects, forceUpdate, setMaterialVersion, setTransformVersion]
  );

  // After sceneObjects update, restore selectedObjects from UUIDs
  useEffect(() => {
    if (pendingSelectedObjectsUUIDs.current && sceneObjects.length > 0) {
      const uuids = pendingSelectedObjectsUUIDs.current;
      const newSelected = sceneObjects.filter((obj) =>
        uuids.includes(obj.uuid)
      );
      setSelectedObjects(newSelected);
      pendingSelectedObjectsUUIDs.current = null;
      console.log("[restoreConfig] Restored selectedObjects", newSelected);
    }
  }, [sceneObjects]);

  const undoAction = useCallback(() => {
    if (historyIndex > 0) {
      setIsUndoRedoAction(true);
      const prevConfig = history[historyIndex - 1];
      restoreConfig(prevConfig);
      setHistoryIndex(historyIndex - 1);
    }
  }, [historyIndex, history, restoreConfig]);

  // Redo action
  const redoAction = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setIsUndoRedoAction(true);
      const nextConfig = history[historyIndex + 1];
      restoreConfig(nextConfig);
      setHistoryIndex(historyIndex + 1);
    }
  }, [historyIndex, history, restoreConfig]);

  // Track changes to objects and lights for history
  useEffect(() => {
    const trackChanges = () => {
      if (selectedObjects.length > 0 || lights.length > 0) {
        addToHistory();
      }
    };

    // Debounce to avoid too many history entries
    const timeoutId = setTimeout(trackChanges, 500);
    return () => clearTimeout(timeoutId);
  }, [selectedObjects, lights, addToHistory]);

  const handleObjectClick = useCallback(
    (e) => {
      e.stopPropagation();

      // If placing decal, let the decal placement handler do its job (triggered by model's onClick)
      if (isPlacingDecal) {
        return;
      }

      console.log("Object clicked:", e.object.name, e.object);

      if (e.shiftKey) {
        setSelectedObjects((prev) => {
          // Ensure prev is always an array
          const prevArray = Array.isArray(prev) ? prev : [prev].filter(Boolean);

          if (prevArray.includes(e.object)) {
            return prevArray.filter((obj) => obj !== e.object);
          }
          return [...prevArray, e.object];
        });
      } else {
        setSelectedObjects([e.object]);
      }
    },
    [isPlacingDecal, setSelectedObjects]
  );

  // Add debugging for selectedObjects changes
  useEffect(() => {
    console.log("ExperienceNew - selectedObjects changed:", selectedObjects);
    console.log(
      "ExperienceNew - selectedObjects length:",
      selectedObjects?.length
    );
  }, [selectedObjects]);

  const handlePointerMissed = useCallback(() => {
    // If in decal placement mode, a "missed" click (on the canvas background)
    // should probably cancel placement mode, or do nothing to allow model click-through.
    // For now, let's prevent it from deselecting objects if placing decal.
    if (isPlacingDecal) {
      // setIsPlacingDecal(false); // Optionally, cancel placement on missed click
      return;
    }
    setSelectedObjects([]);
    setSelectedLight(null);
    // Do not close the rightPanelTab here, as that's usually handled by clicking outside the panel itself.
  }, [isPlacingDecal, setIsPlacingDecal]);

  // Add the selection group to the scene
  useEffect(() => {
    // This effect runs once to add the selection group to the scene
    return () => {
      // Clean up on unmount
      if (selectionGroupRef.current && selectionGroupRef.current.parent) {
        selectionGroupRef.current.parent.remove(selectionGroupRef.current);
      }
    };
  }, []);

  useEffect(() => {
    // Ensure selectedObjects is always an array
    const objectsArray = Array.isArray(selectedObjects)
      ? selectedObjects
      : [selectedObjects].filter(Boolean);

    while (selectionGroupRef.current.children.length > 0) {
      selectionGroupRef.current.remove(selectionGroupRef.current.children[0]);
    }

    if (objectsArray.length > 0) {
      const center = new THREE.Vector3();
      objectsArray.forEach((obj) => {
        center.add(obj.position);
      });
      center.divideScalar(objectsArray.length);

      selectionGroupRef.current.position.copy(center);

      objectsArray.forEach((obj) => {
        const clone = obj.clone();
        // Make the clone invisible while keeping its geometry for transforms
        clone.traverse((child) => {
          if (child.isMesh) {
            child.visible = false;
          }
        });
        clone.position.sub(center);
        selectionGroupRef.current.add(clone);
      });
    }
  }, [selectedObjects]);

  const updateObjectsFromGroup = useCallback(() => {
    // Ensure selectedObjects is always an array
    const objectsArray = Array.isArray(selectedObjects)
      ? selectedObjects
      : [selectedObjects].filter(Boolean);

    if (objectsArray.length > 0 && selectionGroupRef.current) {
      objectsArray.forEach((obj, index) => {
        if (selectionGroupRef.current.children[index]) {
          const worldPos = new THREE.Vector3();
          selectionGroupRef.current.children[index].getWorldPosition(worldPos);
          obj.position.copy(worldPos);

          obj.rotation.copy(selectionGroupRef.current.rotation);
          obj.scale.copy(selectionGroupRef.current.scale);
        }
      });
    }
  }, [selectedObjects]);

  const handleFileUpload = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setUploadedModel(url);
      setSelectedModel("uploaded");
      setSelectedObjects([]);
    }
  }, []);

  const [lastSelectedModel, setLastSelectedModel] = useState(null);

  const handleSceneUpdate = useCallback((scene) => {
    // Clear previous scene objects when a new model is loaded
    setSceneObjects([]);
    setSelectedObjects([]);

    const stats = calculateModelStats(scene);
    setModelStats(stats);
    setShadowsOnModel(scene);

    const objects = [];
    // Store the initial state of the model objects
    const initialObjects = [];

    scene.traverse((object) => {
      if (object.isMesh) {
        // Only set a default name if the mesh doesn't have one
        if (!object.name) {
          object.name = `Mesh_${object.id}`;
        }
        objects.push(object);

        // Store the initial state of the mesh
        initialObjects.push({
          id: object.uuid,
          name: object.name,
          position: object.position.clone(),
          rotation: object.rotation.clone(),
          scale: object.scale.clone(),
          material: object.material
            ? {
                color: object.material.color
                  ? object.material.color.getHex()
                  : 0xffffff,
                roughness: object.material.roughness,
                metalness: object.material.metalness,
                wireframe: object.material.wireframe,
                transparent: object.material.transparent,
                opacity: object.material.opacity,
              }
            : null,
        });
      }
    });

    // Save initial object state for reset functionality
    modelInitialStateRef.current.objects = initialObjects;

    setSceneObjects(objects);
  }, []);

  // Add this effect to detect model changes
  useEffect(() => {
    if (selectedModel !== lastSelectedModel) {
      // Clear the scene objects when model changes
      setSceneObjects([]);
      setSelectedObjects([]);
      setLastSelectedModel(selectedModel);
    }
  }, [selectedModel, lastSelectedModel]);

  const handleLightMoved = useCallback((lightId, newPosition) => {
    setLightPositionHistory((prev) => ({
      ...prev,
      [lightId]: [...(prev[lightId] || []), newPosition],
    }));
  }, []);

  const undoLightMove = useCallback((lightId) => {
    setLightPositionHistory((prev) => {
      const history = prev[lightId] || [];
      if (history.length <= 1) return prev;

      const newHistory = history.slice(0, -1);
      const lastPosition = newHistory[newHistory.length - 1];

      if (lightRefs.current[lightId]) {
        lightRefs.current[lightId].position.copy(lastPosition);
      }

      return {
        ...prev,
        [lightId]: newHistory,
      };
    });
  }, []);

  useEffect(() => {
    const handleKeyDown = (event) => {
      // Handle undo/redo keyboard shortcuts
      if (event.ctrlKey) {
        if (event.key === "z") {
          event.preventDefault();
          undoAction();
          return;
        }
        if (event.key === "y") {
          event.preventDefault();
          redoAction();
          return;
        }
      }

      // Handle Escape key for decal placement
      if (event.key === "Escape" && isPlacingDecal) {
        setIsPlacingDecal(false);
        return;
      }

      // Handle light undo
      if (event.ctrlKey && event.key === "z" && selectedLight) {
        undoLightMove(selectedLight);
      }

      // Handle transform mode shortcuts
      if (selectedObjects.length > 0) {
        switch (event.key) {
          case "g":
            setTransformMode("translate");
            break;
          case "r":
            setTransformMode("rotate");
            break;
          case "s":
            setTransformMode("scale");
            break;
          case "Escape":
            setSelectedObjects([]);
            break;
        }
      }

      // Handle select all
      if (event.ctrlKey && event.key === "a") {
        event.preventDefault();
        if (sceneObjects.length > 0) {
          setSelectedObjects([...sceneObjects]);
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [
    selectedLight,
    undoLightMove,
    sceneObjects,
    selectedObjects.length,
    undoAction,
    redoAction,
    isPlacingDecal,
    setIsPlacingDecal,
  ]);

  useEffect(() => {
    return () => {
      if (uploadedModel) {
        URL.revokeObjectURL(uploadedModel);
      }
      if (customHdri) {
        URL.revokeObjectURL(customHdri);
      }
    };
  }, [uploadedModel, customHdri]);

  const setCameraView = useCallback((view) => {
    if (!orbitControlsRef.current) return;

    switch (view) {
      case "front":
        orbitControlsRef.current.object.position.set(0, 3, 5);
        orbitControlsRef.current.target.set(0, 0, 0);
        break;
      case "top":
        orbitControlsRef.current.object.position.set(0, 5, 0);
        orbitControlsRef.current.target.set(0, 0, 0);
        break;
      case "side":
        orbitControlsRef.current.object.position.set(5, 3, 0);
        orbitControlsRef.current.target.set(0, 0, 0);
        break;
      case "perspective":
        orbitControlsRef.current.object.position.set(5, 5, 5);
        orbitControlsRef.current.target.set(0, 0, 0);
        break;
    }
    orbitControlsRef.current.update();
  }, []);

  // Add the panelRef to ExperienceNew instead of in Toolbar
  const panelRef = useRef(null);

  const [activeAnimation, setActiveAnimation] = useState(null);
  const [animationStartTime, setAnimationStartTime] = useState(0);
  const requestRef = useRef();

  // Animation loop handler
  const animateCamera = useCallback(
    (timestamp) => {
      if (!activeAnimation || !orbitControlsRef.current) {
        return;
      }

      // Calculate animation progress (0 to 1)
      const animation = predefinedAnimations.find(
        (a) => a.id === activeAnimation
      );
      if (!animation) return;

      if (animationStartTime === 0) {
        setAnimationStartTime(timestamp);
      }

      const elapsed = timestamp - animationStartTime;
      const progress = (elapsed % animation.duration) / animation.duration;

      // Get the target position (center of selected objects or scene center)
      const target = new THREE.Vector3(0, 0, 0);
      if (selectedObjects.length > 0) {
        // Calculate center of selected objects
        const objectsArray = Array.isArray(selectedObjects)
          ? selectedObjects
          : [selectedObjects].filter(Boolean);

        if (objectsArray.length > 0) {
          const center = new THREE.Vector3();
          objectsArray.forEach((obj) => {
            center.add(obj.position);
          });
          center.divideScalar(objectsArray.length);
          target.copy(center);
        }
      }

      // Animate camera using the animation function
      animation.animate(
        orbitControlsRef.current.object,
        orbitControlsRef.current,
        progress,
        target
      );

      requestRef.current = requestAnimationFrame(animateCamera);
    },
    [activeAnimation, orbitControlsRef, animationStartTime, selectedObjects]
  );

  // Setup animation loop
  useEffect(() => {
    if (activeAnimation) {
      setAnimationStartTime(0);
      requestRef.current = requestAnimationFrame(animateCamera);

      // Disable orbit controls during animation
      if (orbitControlsRef.current) {
        orbitControlsRef.current.enabled = false;
      }
    } else {
      // Re-enable orbit controls when animation stops
      if (orbitControlsRef.current && selectedObjects.length === 0) {
        orbitControlsRef.current.enabled = true;
      }
    }

    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [activeAnimation, animateCamera, selectedObjects]);

  // Handle animation playback from Outliner
  const handlePlayAnimation = useCallback((animationId) => {
    setActiveAnimation(animationId);
  }, []);

  const getEnvironmentPreset = () => {
    return envPreset;
  };

  // Fix the effect that's resetting environment when model changes
  useEffect(() => {
    if (
      (selectedModel === "jewelry03" || selectedModel === "chosenring") &&
      envPreset !== "hdri_15" &&
      envPreset !== "custom" &&
      lastSelectedModel !== selectedModel // Only reset when actually changing to these models
    ) {
      setEnvPreset("hdri_15");
    } else if (
      selectedModel === "testring" &&
      envPreset !== "hdri_metal" &&
      envPreset !== "custom" &&
      lastSelectedModel !== selectedModel
    ) {
      setEnvPreset("hdri_metal");
    } else if (
      selectedModel === "jewelry05" &&
      envPreset !== "city" &&
      envPreset !== "custom" &&
      lastSelectedModel !== selectedModel
    ) {
      setEnvPreset("city");
    } else if (
      (selectedModel === "agapering1" || selectedModel === "agapering2") &&
      envPreset !== "sunset" &&
      envPreset !== "custom" &&
      lastSelectedModel !== selectedModel
    ) {
      setEnvPreset("sunset");
    }
  }, [selectedModel, lastSelectedModel, envPreset]);

  // Set ground type based on selected model
  useEffect(() => {
    if (selectedModel === "testring" && lastSelectedModel !== selectedModel) {
      setGroundType("none");
      setShowEnvironment(true);
    } else if (
      selectedModel !== "testring" &&
      lastSelectedModel !== selectedModel &&
      (groundType === "none" || showEnvironment === true)
    ) {
      setGroundType("backplate");
      setShowEnvironment(false);
    }
  }, [selectedModel, lastSelectedModel, groundType, showEnvironment]);

  // Reset function
  const handleReset = useCallback(() => {
    // Check if there's a saved scene in localStorage
    const savedScene = localStorage.getItem("agape_saved_scene");
    if (savedScene) {
      const confirmReset = window.confirm(
        "There is a saved scene in your browser storage. Resetting will clear this saved scene. Do you want to continue?"
      );
      if (!confirmReset) {
        return;
      }
      // Clear the saved scene from localStorage
      localStorage.removeItem("agape_saved_scene");
    }

    console.log("Resetting scene to initial state");
    // First clear the scene objects
    setSceneObjects([]);
    setSelectedObjects([]);
    setLastSelectedModel(null);

    // Reset model selection and uploaded model
    setSelectedModel("chosenring");
    setUploadedModel(null);

    // Reset lights to default configuration
    setLights([
      {
        id: "defaultDirectional",
        type: "directional",
        position: [2.5, 5.5, 5],
        intensity: 1.0,
        color: "#ffffff",
        name: "Main Directional Light",
        castShadow: true,
        helperVisible: false,
      },
      {
        id: "keyLight",
        type: "point",
        position: [5, 5, 5],
        intensity: 8.0,
        color: "#ffffff",
        name: "Key Light",
      },
      {
        id: "fillLight",
        type: "point",
        position: [-5, 5, -5],
        intensity: 4.0,
        color: "#ffffff",
        name: "Fill Light",
      },
      {
        id: "backLight",
        type: "point",
        position: [0, 5, -5],
        intensity: 2.0,
        color: "#ffffff",
        name: "Back Light",
      },
    ]);

    // Reset environment settings
    setEnvPreset("sunset");
    setBgColor("#363643");
    setShowEnvironment(false);
    setShowLightSpheres(true);
    setCustomHdri(null);
    setEnvIntensity(1.5);
    setEnvBlur(0.35);
    setEnvRotation(0);

    // Reset post-processing settings
    setPostProcessingEnabled(true);
    setPostProcessingSettings({
      autofocus: {
        enabled: false,
        bokehScale: 18,
      },
      bloom: {
        enabled: true,
        intensity: 0.07,
        threshold: 0.8,
        radius: 0.4,
      },
      dof: {
        enabled: false,
        focusDistance: 0,
        aperture: 0.01,
        bokehScale: 3,
      },
      vignette: {
        enabled: true,
        darkness: 0.5,
        offset: 0.1,
      },
    });

    // Reset scene controls
    setShowGrid(true);
    setWireframe(false);
    setGroundType("backplate");

    // Reset camera position
    if (orbitControlsRef.current) {
      orbitControlsRef.current.object.position.set(5, 2, 5);
      orbitControlsRef.current.target.set(0, 1, 0);
      orbitControlsRef.current.update();
    }

    // Reset transform state
    setTransformMode("translate");
    setSelectedLight(null);

    // Clear history
    setHistory([]);
    setHistoryIndex(-1);
    setLightPositionHistory({});

    // Reset model stats
    setModelStats({
      vertices: 0,
      triangles: 0,
      materials: 0,
      animations: 0,
    });

    // Reset right panel tab
    setRightPanelTab(null);

    // Reset active animation
    setActiveAnimation(null);
    setAnimationStartTime(0);
    if (requestRef.current) {
      cancelAnimationFrame(requestRef.current);
    }

    // Clear any custom HDRI or uploaded model URLs
    if (uploadedModel) {
      URL.revokeObjectURL(uploadedModel);
    }
    if (customHdri) {
      URL.revokeObjectURL(customHdri);
    }

    // Force a model reload by setting selectedModel to null first
    setSelectedModel(null);
    // Then set it back to chosenring after a small delay
    setTimeout(() => {
      setSelectedModel("chosenring");
    }, 100);
  }, []);

  const screenshotRef = useRef(null);

  const captureScreenshot = useCallback(() => {
    if (screenshotRef.current) {
      console.log("Taking screenshot");
      screenshotRef.current();
    } else {
      console.error("Screenshot function not available yet");
    }
  }, []);

  // Load saved scene from localStorage on mount
  useEffect(() => {
    const loadSavedScene = () => {
      const savedScene = localStorage.getItem("agape_saved_scene");
      if (savedScene) {
        try {
          const sceneConfig = JSON.parse(savedScene);
          console.log("Loading saved scene:", sceneConfig);

          // Only load the saved configuration on initial load
          if (!lastSelectedModel) {
            console.log(
              "Initial load - setting saved model:",
              sceneConfig.model
            );
            setSelectedModel(sceneConfig.model);

            // Load the rest of the saved scene configuration
            setLights(sceneConfig.lights);
            setEnvPreset(sceneConfig.environment.preset);
            setBgColor(sceneConfig.environment.bgColor);
            setShowEnvironment(sceneConfig.environment.showEnvironment);
            setShowLightSpheres(sceneConfig.environment.showLightSpheres);
            setEnvIntensity(sceneConfig.environment.intensity);
            setEnvBlur(sceneConfig.environment.blur);
            setEnvRotation(sceneConfig.environment.rotation);
            setPostProcessingEnabled(sceneConfig.postProcessing.enabled);
            setPostProcessingSettings(sceneConfig.postProcessing.settings);
            setShowGrid(sceneConfig.sceneControls.showGrid);
            setWireframe(sceneConfig.sceneControls.wireframe);
            setGroundType(sceneConfig.sceneControls.groundType);

            // Restore camera position after a short delay to ensure scene is loaded
            setTimeout(() => {
              if (orbitControlsRef.current && sceneConfig.camera) {
                orbitControlsRef.current.object.position.fromArray(
                  sceneConfig.camera.position
                );
                orbitControlsRef.current.target.fromArray(
                  sceneConfig.camera.target
                );
                orbitControlsRef.current.update();
              }
            }, 1000);
          } else if (configRef.current) {
            // For explicit loads with configRef, do a complete load
            console.log("Explicit load with configRef");
            handleLoadScene(sceneConfig);
          }

          // Create a function to restore materials
          const restoreMaterials = () => {
            if (!sceneConfig.materials || !sceneObjects.length) {
              console.log("No materials to restore or scene objects not ready");
              return;
            }

            console.log(
              "Attempting to restore materials for objects:",
              sceneObjects
            );
            console.log("Saved materials:", sceneConfig.materials);

            // Create a map of saved materials by object name for easier lookup
            const savedMaterialsMap = new Map(
              sceneConfig.materials.map((m) => [m.name, m.material])
            );

            sceneObjects.forEach((obj) => {
              if (obj.material && savedMaterialsMap.has(obj.name)) {
                const savedMaterial = savedMaterialsMap.get(obj.name);
                console.log(
                  "Restoring material for object:",
                  obj.name,
                  savedMaterial
                );

                // Create a new material of the correct type
                let newMaterial;
                const currentColor = obj.material.color
                  ? obj.material.color.getHex()
                  : 0xffffff;

                switch (savedMaterial.type) {
                  case "MeshBasicMaterial":
                    newMaterial = new THREE.MeshBasicMaterial({
                      color: currentColor,
                    });
                    break;
                  case "MeshStandardMaterial":
                    newMaterial = new THREE.MeshStandardMaterial({
                      color: currentColor,
                      roughness: savedMaterial.roughness,
                      metalness: savedMaterial.metalness,
                    });
                    break;
                  case "MeshPhysicalMaterial":
                    newMaterial = new THREE.MeshPhysicalMaterial({
                      color: currentColor,
                      roughness: savedMaterial.roughness,
                      metalness: savedMaterial.metalness,
                      clearcoat: savedMaterial.clearcoat,
                      clearcoatRoughness: savedMaterial.clearcoatRoughness,
                    });
                    break;
                  case "MeshToonMaterial":
                    newMaterial = new THREE.MeshToonMaterial({
                      color: currentColor,
                    });
                    break;
                  case "MeshNormalMaterial":
                    newMaterial = new THREE.MeshNormalMaterial();
                    break;
                  case "MeshRefractionMaterial":
                    // For MeshRefractionMaterial, we need to preserve the original material
                    // since it's a custom shader material with specific properties
                    newMaterial = obj.material;
                    break;
                  default:
                    newMaterial = new THREE.MeshStandardMaterial({
                      color: currentColor,
                    });
                }

                // Set common properties
                if (savedMaterial.color && newMaterial && newMaterial.color) {
                  try {
                    newMaterial.color.set("#" + savedMaterial.color);
                  } catch (error) {
                    console.warn(
                      `Failed to set color for material ${obj.name}:`,
                      error
                    );
                  }
                }
                newMaterial.wireframe = savedMaterial.wireframe;
                newMaterial.transparent = savedMaterial.transparent;
                newMaterial.opacity = savedMaterial.opacity;
                if (savedMaterial.envMapIntensity !== undefined) {
                  newMaterial.envMapIntensity = savedMaterial.envMapIntensity;
                }

                // Apply the new material
                obj.material = newMaterial;
                obj.material.needsUpdate = true;
              } else {
                console.log(
                  "Could not find saved material for object:",
                  obj.name
                );
              }
            });
          };

          // Set up an interval to check for scene objects
          const checkInterval = setInterval(() => {
            if (sceneObjects.length > 0) {
              clearInterval(checkInterval);
              // Add a small delay to ensure all materials are properly initialized
              setTimeout(restoreMaterials, 500);
            }
          }, 100);

          // Clean up interval after 5 seconds if scene objects never become available
          setTimeout(() => {
            clearInterval(checkInterval);
          }, 5000);

          // Restore camera position after a short delay to ensure scene is loaded
          setTimeout(() => {
            if (orbitControlsRef.current && sceneConfig.camera) {
              orbitControlsRef.current.object.position.fromArray(
                sceneConfig.camera.position
              );
              orbitControlsRef.current.target.fromArray(
                sceneConfig.camera.target
              );
              orbitControlsRef.current.update();
            }
          }, 1000);
        } catch (error) {
          console.error("Error loading saved scene:", error);
        }
      }
    };

    // Load saved scene immediately on mount
    loadSavedScene();

    // Also set up an interval to check for scene readiness
    const checkSceneReady = setInterval(() => {
      if (sceneRef.current && orbitControlsRef.current) {
        clearInterval(checkSceneReady);
        loadSavedScene();
      }
    }, 100);

    return () => clearInterval(checkSceneReady);
  }, [sceneObjects, selectedModel]);

  // Add effect to watch for scene object changes
  useEffect(() => {
    if (sceneObjects.length > 0 && configRef.current) {
      const applyMaterials = () => {
        if (!configRef.current || !sceneObjects.length) {
          console.log("No config or scene objects available yet");
          return;
        }

        console.log("Applying materials to scene objects:", sceneObjects);
        console.log(
          "Saved materials configuration:",
          configRef.current.materials
        );

        sceneObjects.forEach((obj) => {
          // Try to find material config by name first, then by ID
          const materialConfig = configRef.current.materials.find(
            (m) => m.name === obj.name || m.id === obj.uuid
          );

          if (materialConfig && materialConfig.material) {
            console.log(
              "Found material config for object:",
              obj.name,
              materialConfig.material
            );
            const savedMaterial = materialConfig.material;
            let newMaterial;

            // Create a new material of the correct type with initial properties
            switch (savedMaterial.type) {
              case "MeshBasicMaterial":
                newMaterial = new THREE.MeshBasicMaterial({
                  color: new THREE.Color("#" + savedMaterial.color),
                  wireframe: savedMaterial.wireframe,
                  transparent: savedMaterial.transparent,
                  opacity: savedMaterial.opacity,
                });
                break;
              case "MeshStandardMaterial":
                newMaterial = new THREE.MeshStandardMaterial({
                  color: new THREE.Color("#" + savedMaterial.color),
                  roughness: savedMaterial.roughness,
                  metalness: savedMaterial.metalness,
                  wireframe: savedMaterial.wireframe,
                  transparent: savedMaterial.transparent,
                  opacity: savedMaterial.opacity,
                  envMapIntensity: savedMaterial.envMapIntensity,
                });
                break;
              case "MeshPhysicalMaterial":
                newMaterial = new THREE.MeshPhysicalMaterial({
                  color: new THREE.Color("#" + savedMaterial.color),
                  roughness: savedMaterial.roughness,
                  metalness: savedMaterial.metalness,
                  clearcoat: savedMaterial.clearcoat,
                  clearcoatRoughness: savedMaterial.clearcoatRoughness,
                  wireframe: savedMaterial.wireframe,
                  transparent: savedMaterial.transparent,
                  opacity: savedMaterial.opacity,
                  envMapIntensity: savedMaterial.envMapIntensity,
                });
                break;
              case "MeshToonMaterial":
                newMaterial = new THREE.MeshToonMaterial({
                  color: new THREE.Color("#" + savedMaterial.color),
                  wireframe: savedMaterial.wireframe,
                  transparent: savedMaterial.transparent,
                  opacity: savedMaterial.opacity,
                });
                break;
              case "MeshNormalMaterial":
                newMaterial = new THREE.MeshNormalMaterial({
                  wireframe: savedMaterial.wireframe,
                });
                break;
              case "MeshRefractionMaterial":
                // For MeshRefractionMaterial, we need to preserve the original material
                // since it's a custom shader material with specific properties
                newMaterial = obj.material;
                if (newMaterial) {
                  newMaterial.aberrationStrength =
                    savedMaterial.aberrationStrength;
                  newMaterial.toneMapped = savedMaterial.toneMapped;
                  newMaterial.ior = savedMaterial.ior;
                  newMaterial.colorEnvMapRotY = savedMaterial.colorEnvMapRotY;
                }
                break;
              default:
                newMaterial = new THREE.MeshStandardMaterial({
                  color: new THREE.Color("#" + savedMaterial.color),
                  roughness: savedMaterial.roughness,
                  metalness: savedMaterial.metalness,
                  wireframe: savedMaterial.wireframe,
                  transparent: savedMaterial.transparent,
                  opacity: savedMaterial.opacity,
                  envMapIntensity: savedMaterial.envMapIntensity,
                });
            }

            if (newMaterial) {
              // Apply the new material
              obj.material = newMaterial;
              obj.material.needsUpdate = true;
              console.log("Applied material to object:", obj.name, newMaterial);
            }
          } else {
            console.log("No material config found for object:", obj.name);
          }
        });
      };

      // Add a small delay to ensure all materials are properly initialized
      const timeoutId = setTimeout(applyMaterials, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [sceneObjects]);

  const handleLoadScene = (config) => {
    try {
      console.log("Loading scene configuration:", config);

      // Store the config in the ref
      configRef.current = config;

      // Clear scene objects and selected objects first
      setSceneObjects([]);
      setSelectedObjects([]);
      setLastSelectedModel(null);

      // Restore scene settings
      setSelectedModel(config.model);
      setLights(config.lights);
      setEnvPreset(config.environment.preset);
      setBgColor(config.environment.bgColor);
      setShowEnvironment(config.environment.showEnvironment);
      setShowLightSpheres(config.environment.showLightSpheres);
      setEnvIntensity(config.environment.intensity);
      setEnvBlur(config.environment.blur);
      setEnvRotation(config.environment.rotation);
      setPostProcessingEnabled(config.postProcessing.enabled);
      setPostProcessingSettings(config.postProcessing.settings);
      setShowGrid(config.sceneControls.showGrid);
      setWireframe(config.sceneControls.wireframe);
      setGroundType(config.sceneControls.groundType);

      // Wait for the scene to update before applying camera position
      setTimeout(() => {
        if (orbitControlsRef.current && config.camera) {
          orbitControlsRef.current.object.position.fromArray(
            config.camera.position
          );
          orbitControlsRef.current.target.fromArray(config.camera.target);
          orbitControlsRef.current.update();
        }
      }, 1000);
    } catch (error) {
      console.error("Error loading scene configuration:", error);
    }
  };

  const handleSaveScene = (sceneName, saveType) => {
    // Create a scene configuration object
    const sceneConfig = {
      name: sceneName,
      timestamp: new Date().toISOString(),
      model: selectedModel,
      lights: lights.map((light) => ({
        ...light,
        position:
          lightRefs.current[light.id]?.position.toArray() || light.position,
      })),
      environment: {
        preset: envPreset,
        intensity: envIntensity,
        blur: envBlur,
        rotation: envRotation,
        showEnvironment,
        bgColor,
        customHdri,
      },
      postProcessing: {
        enabled: postProcessingEnabled,
        settings: postProcessingSettings,
      },
      sceneControls: {
        showGrid,
        wireframe,
        groundType,
        showLightSpheres,
      },
      camera: {
        position: orbitControlsRef.current?.object.position.toArray(),
        target: orbitControlsRef.current?.target.toArray(),
      },
      materials: sceneObjects.map((obj) => {
        const material = obj.material;
        if (!material) return { id: obj.uuid, name: obj.name, material: null };

        return {
          id: obj.uuid,
          name: obj.name,
          material: {
            type: material.type,
            color: material.color ? material.color.getHexString() : "ffffff",
            roughness: material.roughness,
            metalness: material.metalness,
            wireframe: material.wireframe,
            transparent: material.transparent,
            opacity: material.opacity,
            envMapIntensity: material.envMapIntensity,
            // Add specific properties for MeshRefractionMaterial
            ...(material.type === "MeshRefractionMaterial" && {
              aberrationStrength: material.aberrationStrength,
              toneMapped: material.toneMapped,
              ior: material.ior,
              colorEnvMapRotY: material.colorEnvMapRotY,
            }),
          },
        };
      }),
    };

    if (saveType === "local") {
      // Save to localStorage
      localStorage.setItem("agape_saved_scene", JSON.stringify(sceneConfig));
    } else {
      // Export as file
      const blob = new Blob([JSON.stringify(sceneConfig, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${sceneName.replace(/\s+/g, "_")}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Engraving state
  const [engravingText, setEngravingText] = useState("");
  const [decalPosition, setDecalPosition] = useState([0, 1.68, 0]);
  const [decalScale, setDecalScale] = useState([2.8, 0.9, 1.5]);
  const [decalRotation, setDecalRotation] = useState([-Math.PI / 2, 0, 0]);
  const [decalColor, setDecalColor] = useState("#cbcbcb");
  const [selectedFont, setSelectedFont] = useState(
    "/fonts/ttf/DancingScript-Regular.ttf"
  );

  const handleEngraveText = useCallback((text) => {
    setEngravingText(text);
    // Store in localStorage to make it persist across panel changes and page reloads
    localStorage.setItem("engravingText", text);
  }, []);

  const handleFontChange = useCallback((font) => {
    setSelectedFont(font);
    localStorage.setItem("engravingFont", font);
  }, []);

  // Load saved engraving text on mount
  useEffect(() => {
    const savedEngravingText = localStorage.getItem("engravingText");
    if (savedEngravingText) {
      setEngravingText(savedEngravingText);
    }
  }, []);

  const handleDecalPlacement = useCallback(
    (event) => {
      if (!event || !event.point || !event.face || !event.object) {
        console.warn("Decal placement click missing event data", event);
        setIsPlacingDecal(false);
        return;
      }
      event.stopPropagation();

      const { point: worldPoint, face, object: clickedMesh } = event;

      const localPoint = clickedMesh.worldToLocal(worldPoint.clone());
      setDecalPosition([localPoint.x, localPoint.y, localPoint.z]);

      const tempObject = new THREE.Object3D();

      // Normal transformation: world normal to local normal for lookAt target
      const inverseWorldMatrix = clickedMesh.matrixWorld.clone().invert();
      const localNormalMatrix = new THREE.Matrix3().getNormalMatrix(
        inverseWorldMatrix
      );
      const localNormalForLookAt = face.normal
        .clone()
        .applyMatrix3(localNormalMatrix)
        .normalize();
      const localLookAtTarget = localPoint.clone().add(localNormalForLookAt);

      tempObject.lookAt(localLookAtTarget); // Orients -Z along the direction, Y up by default

      // Combine with a fixed rotation for text orientation (e.g., text is on XY plane of decal, decal plane needs to be upright)
      // The default decalRotation was often [Math.PI / 2, 0, 0] or similar.
      const qSurface = tempObject.quaternion.clone();
      const qTextOnDecal = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(Math.PI / 2, 0, 0, "XYZ")
      ); // Default orientation adjustment

      const qFinal = qSurface.multiply(qTextOnDecal); // Apply text orientation after surface alignment
      const finalEuler = new THREE.Euler().setFromQuaternion(qFinal, "XYZ");

      setDecalRotation([finalEuler.x, finalEuler.y, finalEuler.z]);
      setIsPlacingDecal(false); // Turn off placement mode
      console.log(
        "Decal placed at local:",
        [localPoint.x, localPoint.y, localPoint.z],
        "with rotation:",
        [finalEuler.x, finalEuler.y, finalEuler.z]
      );
    },
    [setDecalPosition, setDecalRotation, setIsPlacingDecal]
  );

  // Get predefined decal positions based on selected model
  const getDefaultDecalPosition = useCallback((modelId) => {
    const modelConfigs = {
      chosenring: [0, 1.68, 0],
      testring: [0, 1.68, 0],
      agapering1: [0, 1.2, 0],
      agapering2: [0, 1.2, 0],
      ringa: [0, 0.26, 0],
      ringb: [0, 0.5, 0],
      ringc: [0, 0.26, 0],
    };
    return modelConfigs[modelId] || [0, 1.68, 0];
  }, []);

  const getDefaultDecalRotation = useCallback((modelId) => {
    const modelConfigs = {
      chosenring: [-Math.PI / 2, 0, 0],
      testring: [-Math.PI / 2, 0, 0],
      agapering1: [-Math.PI / 2, 0, 0],
      agapering2: [-Math.PI / 2, 0, 0],
      ringa: [-Math.PI / 2, 0, 0],
      ringb: [-Math.PI / 2, 0, 0],
      ringc: [-Math.PI / 2, 0, 0],
    };
    return modelConfigs[modelId] || [-Math.PI / 2, 0, 0];
  }, []);

  const getDefaultDecalScale = useCallback((modelId) => {
    const modelConfigs = {
      chosenring: [6.0, 2.0, 1.5],
      testring: [6.0, 2.0, 1.5],
      agapering1: [3.2, 1.8, 1.0],
      agapering2: [3.2, 1.8, 1.0],
      ringa: [1.8, 2.0, 0.8],
      ringb: [1.8, 2.0, 0.8],
      ringc: [1.8, 2.0, 0.8],
    };
    return modelConfigs[modelId] || [4.2, 2.0, 1.5];
  }, []);

  // Calculate dynamic scale based on text length and model type
  const calculateDynamicScale = useCallback((baseScale, text, modelId) => {
    if (!text || text.length === 0) return baseScale;

    const textLength = text.length;
    let [scaleX, scaleY, scaleZ] = baseScale;

    // Calculate scale multiplier based on text length
    let scaleMultiplier = 1.0;

    if (textLength > 10) {
      // For very long text, scale up more aggressively
      scaleMultiplier = 1.3 + (textLength - 10) * 0.08;
    } else if (textLength > 6) {
      // For medium length text, scale up moderately
      scaleMultiplier = 1.0 + (textLength - 6) * 0.08;
    } else if (textLength < 4) {
      // For short text, scale down slightly to prevent oversized appearance
      scaleMultiplier = 0.8 + textLength * 0.05;
    }

    // Apply text length scaling to X-axis (width) primarily
    scaleX = scaleX * scaleMultiplier;

    // For very long text, also adjust the height slightly
    if (textLength > 8) {
      scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);
    }

    // Cap maximum scale to prevent excessive sizing (updated for larger Y values)
    const maxScale = {
      chosenring: [12.0, 2.5, 2.0],
      testring: [12.0, 2.5, 2.0],
      agapering1: [6.5, 2.2, 1.5],
      agapering2: [6.5, 2.2, 1.5],
      ringa: [4.0, 2.5, 1.2],
      ringb: [4.0, 2.5, 1.2],
      ringc: [4.0, 2.5, 1.2],
    };

    const limits = maxScale[modelId] || [8.5, 2.5, 2.0];
    scaleX = Math.min(scaleX, limits[0]);
    scaleY = Math.min(scaleY, limits[1]);
    scaleZ = Math.min(scaleZ, limits[2]);

    return [scaleX, scaleY, scaleZ];
  }, []);

  // Update scale when text changes
  useEffect(() => {
    if (selectedModel && engravingText) {
      const baseScale = getDefaultDecalScale(selectedModel);
      const dynamicScale = calculateDynamicScale(
        baseScale,
        engravingText,
        selectedModel
      );
      setDecalScale(dynamicScale);
    }
  }, [
    engravingText,
    selectedModel,
    getDefaultDecalScale,
    calculateDynamicScale,
  ]);

  // Initialize decal position based on selected model
  useEffect(() => {
    if (selectedModel) {
      setDecalPosition(getDefaultDecalPosition(selectedModel));
      setDecalRotation(getDefaultDecalRotation(selectedModel));
      // Only set initial scale if there's no engraving text yet
      if (!engravingText) {
        setDecalScale(getDefaultDecalScale(selectedModel));
      }
    }
  }, [
    selectedModel,
    getDefaultDecalPosition,
    getDefaultDecalRotation,
    getDefaultDecalScale,
    engravingText,
  ]);

  const [isFullscreen, setIsFullscreen] = useState(false);

  function getFullConfig() {
    return JSON.parse(
      JSON.stringify({
        selectedModel,
        uploadedModel,
        lights,
        envPreset,
        bgColor,
        showEnvironment,
        showLightSpheres,
        customHdri,
        envIntensity,
        envBlur,
        envRotation,
        postProcessingEnabled,
        postProcessingSettings,
        showGrid,
        wireframe,
        groundType,
        selectedObjects: selectedObjects.map((obj) => obj.uuid),
        // Store object states for transforms and materials
        objectStates: sceneObjects.map((obj) => ({
          uuid: obj.uuid,
          name: obj.name,
          position: obj.position.toArray(),
          rotation: obj.rotation.toArray(),
          scale: obj.scale.toArray(),
          material: obj.material
            ? {
                type: obj.material.type,
                color: obj.material.color
                  ? obj.material.color.getHexString()
                  : "ffffff",
                roughness: obj.material.roughness,
                metalness: obj.material.metalness,
                clearcoat: obj.material.clearcoat,
                clearcoatRoughness: obj.material.clearcoatRoughness,
                wireframe: obj.material.wireframe,
                transparent: obj.material.transparent,
                opacity: obj.material.opacity,
                envMapIntensity: obj.material.envMapIntensity,
              }
            : null,
        })),
      })
    );
  }

  useEffect(() => {
    const handler = setTimeout(() => {
      addToHistory();
    }, 150);
    return () => clearTimeout(handler);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    selectedModel,
    uploadedModel,
    lights,
    envPreset,
    bgColor,
    showEnvironment,
    showLightSpheres,
    customHdri,
    envIntensity,
    envBlur,
    envRotation,
    postProcessingEnabled,
    postProcessingSettings,
    showGrid,
    wireframe,
    groundType,
    transformVersion,
    materialVersion,
  ]);

  return (
    <>
      <ToastContainer />
      <Header
        onReset={handleReset}
        onScreenshot={captureScreenshot}
        onSaveScene={handleSaveScene}
        onLoadScene={handleLoadScene}
        isFullscreen={isFullscreen}
        onToggleFullscreen={() => setIsFullscreen(!isFullscreen)}
        onUndo={undoAction}
        onRedo={redoAction}
      />

      {/* Left Toolbar */}
      <div
        className={`fixed left-5 top-1/2 transform -translate-y-1/2 z-[1000] transition-all duration-300 ${
          isFullscreen ? "opacity-0 pointer-events-none" : "opacity-100"
        }`}
      >
        <Toolbar
          transformMode={transformMode}
          setTransformMode={setTransformMode}
          undoAction={undoAction}
          redoAction={redoAction}
          rightPanelTab={rightPanelTab}
          setRightPanelTab={setRightPanelTab}
          orientation="vertical"
          sceneObjects={sceneObjects}
          setSelectedObjects={setSelectedObjects}
          selectedObjects={selectedObjects}
          panelRef={panelRef}
        />
      </div>

      {/* Dropdown Panels for each toolbar option - appears beside toolbar when selected */}
      {rightPanelTab && (
        <div
          ref={panelRef}
          className="fixed left-20 top-1/2 transform -translate-y-1/2 bg-gradient-to-tl from-[#32343D] to-[#14192D] p-4 rounded-lg shadow-lg z-[1000] max-h-[80vh] overflow-y-auto w-[300px] border border-[#A3A3A3]/30"
        >
          {rightPanelTab === "transform" && (
            <TransformPanel
              selectedObjects={selectedObjects}
              transformMode={transformMode}
              setTransformMode={setTransformMode}
              undoAction={undoAction}
              redoAction={redoAction}
              addToHistory={addToHistory}
            />
          )}

          {rightPanelTab === "material" && (
            <MaterialPanel
              selectedObjects={selectedObjects}
              forceUpdate={forceUpdate}
              setMaterialVersion={setMaterialVersion}
              addToHistory={addToHistory}
            />
          )}

          {rightPanelTab === "postprocessing" && (
            <PostProcessingPanel
              postProcessingEnabled={postProcessingEnabled}
              setPostProcessingEnabled={setPostProcessingEnabled}
              postProcessingSettings={postProcessingSettings}
              setPostProcessingSettings={setPostProcessingSettings}
              addToHistory={addToHistory}
            />
          )}

          {rightPanelTab === "environment" && (
            <EnvironmentPanel
              envPreset={envPreset}
              setEnvPreset={setEnvPreset}
              bgColor={bgColor}
              setBgColor={setBgColor}
              showEnvironment={showEnvironment}
              setShowEnvironment={setShowEnvironment}
              customHdri={customHdri}
              setCustomHdri={setCustomHdri}
              envIntensity={envIntensity}
              setEnvIntensity={setEnvIntensity}
              showModelStats={showModelStats}
              setShowModelStats={setShowModelStats}
              envBlur={envBlur}
              setEnvBlur={setEnvBlur}
              envRotation={envRotation}
              setEnvRotation={setEnvRotation}
              addToHistory={addToHistory}
            />
          )}

          {rightPanelTab === "lights" && (
            <LightPanel
              lights={lights}
              setLights={setLights}
              lightRefs={lightRefs}
              showGrid={showGrid}
              setShowGrid={setShowGrid}
              wireframe={wireframe}
              setWireframe={setWireframe}
              showLightSpheres={showLightSpheres}
              setShowLightSpheres={setShowLightSpheres}
              addToHistory={addToHistory}
            />
          )}

          {rightPanelTab === "engraving" && (
            <EngravingPanel
              onEngraveText={handleEngraveText}
              onDecalPositionChange={setDecalPosition}
              onDecalScaleChange={setDecalScale}
              onDecalRotationChange={setDecalRotation}
              decalPosition={decalPosition}
              decalScale={decalScale}
              decalRotation={decalRotation}
              decalColor={decalColor}
              onDecalColorChange={setDecalColor}
              onFontChange={handleFontChange}
              selectedFont={selectedFont}
              isPlacingDecal={isPlacingDecal}
              setIsPlacingDecal={setIsPlacingDecal}
              showDebug={showDecalDebug}
              onShowDebugChange={setShowDecalDebug}
            />
          )}
        </div>
      )}

      {/* Right Panel - Collapsible Outliner */}
      <div
        className={`fixed right-5 top-24 z-[1000] transition-all duration-300 ${
          isFullscreen ? "opacity-0 pointer-events-none" : "opacity-100"
        }`}
      >
        <div className="bg-gradient-to-tl from-[#32343D] to-[#14192D] shadow-lg rounded-lg overflow-visible">
          <Outliner
            sceneObjects={sceneObjects}
            selectedObjects={selectedObjects}
            onSelectObject={setSelectedObjects}
            onCameraView={setCameraView}
            onPlayAnimation={handlePlayAnimation}
            onSaveScene={handleSaveScene}
            groundType={groundType}
            setGroundType={setGroundType}
          />
        </div>
      </div>

      {/* Scene Switcher - Bottom center floating component */}
      <div
        className={`fixed left-1/2 bottom-5 transform -translate-x-1/2 bg-gradient-to-tl from-[#32343D] to-[#0F132A] hover:bg-gradient-to-tl hover:from-[#32343D] hover:to-[#14192D] p-2 rounded-lg shadow-lg z-[1000] transition-all duration-300 ${
          isFullscreen ? "opacity-0 pointer-events-none" : "opacity-100"
        }`}
      >
        <SceneSwitcher
          selectedModel={selectedModel}
          onSelectModel={setSelectedModel}
        />
      </div>

      {/* Canvas - adjust to account for header and toolbar */}
      <div
        className="w-full h-full"
        style={{
          background: "linear-gradient(to bottom right, #363643, #5C5C6E)",
        }}
      >
        <Canvas
          className="w-full h-full"
          camera={{ position: [5, 2, 5], fov: 50, near: 0.1, far: 50 }}
          shadows
          ref={sceneRef}
          gl={{
            antialias: true,
            preserveDrawingBuffer: true,
          }}
          onPointerMissed={handlePointerMissed}
          raycaster={{
            computeOffsets: (e) => ({ offsetX: e.clientX, offsetY: e.clientY }),
          }}
          frameloop="demand"
        >
          <Suspense fallback={null}>
            <fog attach="fog" args={[bgColor || "#363643", 20, 70]} />
            <fogExp2 attach="fog" args={[bgColor || "#363643", 0.045]} />

            <ScreenshotTaker
              onScreenshotRef={(fn) => {
                screenshotRef.current = fn;
              }}
            />

            <SceneLights
              lights={lights}
              lightRefs={lightRefs}
              selectedLight={selectedLight}
              onLightSelect={setSelectedLight}
              onLightMoved={handleLightMoved}
              showLightSpheres={showLightSpheres}
              threePointLighting={true}
            />
            <Environment
              preset={getEnvironmentPreset()}
              background={showEnvironment}
              bgColor={bgColor}
              customHdri={customHdri}
              intensity={envIntensity}
              blur={envBlur}
              envRotation={envRotation}
            />

            <SelectionOutline selectedObjects={selectedObjects}>
              <ModelStage
                selectedModel={selectedModel}
                wireframe={wireframe}
                handleSceneUpdate={handleSceneUpdate}
                handleObjectClick={handleObjectClick}
                uploadedModel={uploadedModel}
                groundType={groundType}
                engravingText={engravingText}
                activeAnimation={activeAnimation}
                decalPosition={decalPosition}
                decalScale={decalScale}
                decalRotation={decalRotation}
                decalColor={decalColor}
                font={selectedFont}
                isPlacingDecal={isPlacingDecal}
                onDecalPlacement={handleDecalPlacement}
                showDecalDebug={showDecalDebug}
              />
            </SelectionOutline>

            <OrbitControls
              ref={orbitControlsRef}
              target={[0, 1, 0]}
              makeDefault
              enableDamping={false}
            />

            {selectedObjects.length > 0 && rightPanelTab === "transform" && (
              <TransformControls
                object={
                  selectedObjects.length === 1
                    ? selectedObjects[0]
                    : selectionGroupRef.current
                }
                mode={transformMode}
                onMouseDown={() => {
                  if (orbitControlsRef.current)
                    orbitControlsRef.current.enabled = false;
                }}
                onMouseUp={() => {
                  if (orbitControlsRef.current)
                    orbitControlsRef.current.enabled = true;
                  if (selectedObjects.length > 1) updateObjectsFromGroup();
                }}
                onObjectChange={() => {
                  if (selectedObjects.length > 1) updateObjectsFromGroup();
                  forceUpdate();
                  setTransformVersion((v) => v + 1);
                }}
              />
            )}
            {groundType === "grid" && (
              <gridHelper
                args={[100, 100, "#666666", "#333333"]}
                receiveShadow
              />
            )}
            {groundType === "solid" && (
              <mesh
                rotation-x={-Math.PI / 2}
                receiveShadow
                position={[0, -0.01, 0]}
              >
                <planeGeometry args={[200, 200]} />
                <meshStandardMaterial
                  color="#5d5d5d"
                  wireframe={wireframe}
                  transparent={true}
                  opacity={0.7}
                />
              </mesh>
            )}
            {groundType === "reflective" && (
              <mesh
                receiveShadow
                rotation={[-Math.PI / 2, 0, 0]}
                position={[0, -0.01, 0]}
              >
                <planeGeometry args={[200, 200]} />
                <MeshReflectorMaterial
                  blur={[800, 200]}
                  resolution={2048}
                  mixBlur={0.6}
                  mixStrength={30}
                  roughness={0.9}
                  depthScale={1}
                  minDepthThreshold={0.4}
                  maxDepthThreshold={1.2}
                  color="#1a1a1a"
                  metalness={0.4}
                  mirror={0.3}
                  transparent={true}
                  opacity={0.85}
                />
              </mesh>
            )}
            {groundType === "none" && (
              <mesh
                receiveShadow
                rotation={[-Math.PI / 2, 0, 0]}
                position={[0, -0.01, 0]}
              >
                <planeGeometry args={[200, 200]} />
                <primitive
                  object={
                    new THREE.ShadowMaterial({
                      opacity: 0.1,
                      color: new THREE.Color(0x000000),
                      transparent: true,
                    })
                  }
                />
              </mesh>
            )}
            <GizmoHelper alignment="bottom-right" margin={[80, 80]}>
              <GizmoViewport
                labelColor="white"
                axisColors={["red", "green", "blue"]}
              />
            </GizmoHelper>

            <primitive object={selectionGroupRef.current} />

            <PostProcessingEffects
              enabled={postProcessingEnabled}
              settings={postProcessingSettings}
            />
          </Suspense>
        </Canvas>
      </div>

      {showModelStats && <ModelStats stats={modelStats} />}
    </>
  );
}
