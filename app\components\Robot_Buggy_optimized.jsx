/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 public/models/Robot_Buggy_optimized.glb 
*/

import React from "react";
import { useGLTF } from "@react-three/drei";

export function Model(props) {
  const { nodes, materials } = useGLTF("/models/Robot_Buggy_optimized.glb");
  return (
    <group {...props} dispose={null}>
      <mesh
        geometry={nodes.Cube136.geometry}
        material={materials.LawnMower_UV01}
        position={[0, 0.303, -0.251]}
        scale={1.004}
      />
      <mesh
        geometry={nodes["1001"].geometry}
        material={materials["Patrol_Decals.004"]}
        position={[0.272, 0.433, 0.107]}
        rotation={[0.024, 0.044, 0.328]}
        scale={0.279}
      />
      <mesh
        geometry={nodes["1030"].geometry}
        material={materials["Trim.001"]}
        position={[0, 0.322, -0.155]}
        rotation={[-Math.PI, 0, -Math.PI]}
        scale={0.735}
      />
      <mesh
        geometry={nodes.Cube052.geometry}
        material={materials["LawnMower_UV03.001"]}
        position={[0, 0.253, -0.377]}
        scale={0.857}
      />
      <mesh
        geometry={nodes.Cube053.geometry}
        material={materials["Patrol_Decals.004"]}
        position={[0, 0.314, 0.006]}
        scale={0.573}
      />
      <mesh
        geometry={nodes.Cube139.geometry}
        material={materials["LawnMower_UV02.001"]}
        position={[0, 0.363, -0.296]}
        scale={0.889}
      />
      <mesh
        geometry={nodes.Cube142.geometry}
        material={materials["LawnMower_UV02.002"]}
        position={[0, 0.266, -0.239]}
        scale={0.715}
      />
      <mesh
        geometry={nodes.Cube144.geometry}
        material={materials["Trim.001"]}
        position={[0, 0.123, -0.198]}
        scale={0.844}
      />
    </group>
  );
}

useGLTF.preload("/models/Robot_Buggy_optimized.glb");
