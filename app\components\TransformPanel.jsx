"use client";

import { useState, useEffect } from "react";
import * as THREE from "three";
import {
  Undo2,
  Redo2,
  RotateCcw,
  Move,
  RotateCw,
  Maximize,
} from "lucide-react";
import { useHistoryDebounce } from "../hooks/useDebounce";

// Reusable transform control group with slider+input combination
const TransformControlGroup = ({
  label,
  value,
  onChange,
  axis,
  disabled = false,
  min = -10,
  max = 10,
}) => (
  <div className={`mb-2 ${disabled ? "opacity-50" : ""}`}>
    <div className="flex items-center justify-between mb-1">
      <label className="text-[10px] md:text-xs text-[#FDE9CE]">{label}</label>
      <input
        type="number"
        value={value.toFixed(2)}
        onChange={(e) => {
          const val = parseFloat(e.target.value);
          if (!isNaN(val)) onChange(axis, val, false); // false = not slider
        }}
        disabled={disabled}
        className="w-16 p-1 rounded bg-[#3A3D4A] text-[#FDE9CE] border border-gray-700 text-right text-[10px] md:text-xs"
      />
    </div>
    <input
      type="range"
      min={min}
      max={max}
      step="0.1"
      value={value}
      onChange={(e) => onChange(axis, parseFloat(e.target.value), true)} // true = slider
      disabled={disabled}
      className="w-full h-2 accent-blue-500 bg-[#3A3D4A]"
    />
  </div>
);

// Button component for transform mode selection
const ModeButton = ({ active, onClick, children }) => (
  <button
    onClick={onClick}
    className={`px-3 py-1 rounded ${
      active
        ? "bg-blue-500 text-white"
        : "bg-[#3A3D4A] text-[#FDE9CE] hover:bg-[#4A4E5A]"
    }`}
  >
    {children}
  </button>
);

export function TransformPanel({
  selectedObjects,
  transformMode,
  setTransformMode,
  undoAction,
  redoAction,
  addToHistory,
}) {
  const [localTransformMode, setLocalTransformMode] = useState(
    transformMode || "translate"
  );
  const [uniformScale, setUniformScale] = useState(false);
  const [activeTab, setActiveTab] = useState("position");

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    300
  );

  // Update transform mode when prop changes
  useEffect(() => {
    if (transformMode) {
      setLocalTransformMode(transformMode);
      // Map transform mode to corresponding tab
      if (transformMode === "translate") setActiveTab("position");
      if (transformMode === "rotate") setActiveTab("rotation");
      if (transformMode === "scale") setActiveTab("scale");
    }
  }, [transformMode]);

  // Update transform mode prop when local state changes
  useEffect(() => {
    if (setTransformMode) setTransformMode(localTransformMode);
  }, [localTransformMode, setTransformMode]);

  // Get the first selected object for reference
  const selectedObject =
    Array.isArray(selectedObjects) && selectedObjects.length > 0
      ? selectedObjects[0]
      : null;

  const hasSelection =
    selectedObjects &&
    (Array.isArray(selectedObjects)
      ? selectedObjects.length > 0
      : selectedObjects);

  // Helper functions for updating transforms
  const updateTransform = (property, axis, value, isSlider = false) => {
    if (!selectedObject) return;

    // Update the reference object
    selectedObject[property][axis] = value;

    // Update all other selected objects
    if (Array.isArray(selectedObjects) && selectedObjects.length > 1) {
      selectedObjects.forEach((obj) => {
        if (obj !== selectedObject) {
          obj[property][axis] = value;
        }
      });
    }

    // Add to history with appropriate debouncing
    if (addToHistory) {
      if (isSlider) {
        addToHistoryDebounced();
      } else {
        addToHistoryImmediate();
      }
    }
  };

  const updateScale = (axis, value, isSlider = false) => {
    if (!selectedObject) return;

    if (uniformScale) {
      // Apply uniform scaling to all axes
      selectedObjects.forEach((obj) => {
        obj.scale.set(value, value, value);
      });
    } else {
      // Only update specified axis
      updateTransform("scale", axis, value, isSlider);
      return; // updateTransform already handles history
    }

    // Add to history for uniform scaling
    if (addToHistory) {
      if (isSlider) {
        addToHistoryDebounced();
      } else {
        addToHistoryImmediate();
      }
    }
  };

  const resetTransform = () => {
    selectedObjects.forEach((obj) => {
      obj.position.set(0, 0, 0);
      obj.rotation.set(0, 0, 0);
      obj.scale.set(1, 1, 1);
    });
  };

  // Function to handle tab click - updates both tab and transform mode
  const handleTabClick = (tab) => {
    setActiveTab(tab);
    // Map tab to corresponding transform mode
    if (tab === "position") setLocalTransformMode("translate");
    if (tab === "rotation") setLocalTransformMode("rotate");
    if (tab === "scale") setLocalTransformMode("scale");
  };

  if (!hasSelection) {
    return (
      <div className="p-4 text-center text-gray-400 bg-[#242636] rounded-lg border border-[#3A3D4A]">
        <div className="mb-2">📦</div>
        Select an object to edit its transform
      </div>
    );
  }

  // Icons for transform modes using Lucide components
  const transformIcons = {
    translate: Move,
    rotate: RotateCw,
    scale: Maximize,
  };

  return (
    <div className="transform-panel p-4 bg-[#242636] rounded-lg border border-[#3A3D4A]">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-[#FDE9CE]">Transform</h3>

        {/* Compact history controls with Lucide icons */}
        <div className="flex gap-1">
          <button
            onClick={undoAction}
            className="p-1.5 bg-[#3A3D4A] text-[#FDE9CE] rounded hover:bg-[#4A4E5A]"
            title="Undo"
          >
            <Undo2 size={16} />
          </button>
          <button
            onClick={redoAction}
            className="p-1.5 bg-[#3A3D4A] text-[#FDE9CE] rounded hover:bg-[#4A4E5A]"
            title="Redo"
          >
            <Redo2 size={16} />
          </button>
        </div>
      </div>

      {/* Selection count */}
      {selectedObjects.length > 1 && (
        <div className="mb-3 p-2 bg-[#3A3D4A]/50 rounded text-[#FDE9CE] text-xs md:text-sm text-center">
          {selectedObjects.length} objects selected
        </div>
      )}

      {selectedObject && (
        <>
          {/* Tabbed interface for transform properties */}
          <div className="mb-4">
            <div className="flex border-b border-[#3A3D4A]">
              {["position", "rotation", "scale"].map((tab) => (
                <button
                  key={tab}
                  className={`px-3 py-2 text-xs md:text-sm flex items-center ${
                    activeTab === tab
                      ? "text-blue-400 border-b-2 border-blue-400"
                      : "text-[#A3A3A3] hover:text-[#FDE9CE]"
                  }`}
                  onClick={() => handleTabClick(tab)}
                >
                  {/* <span className="mr-1">
                    {tab === "position" && <Move size={16} />}
                    {tab === "rotation" && <RotateCw size={16} />}
                    {tab === "scale" && <Maximize size={16} />}
                  </span> */}
                  {tab.charAt(0).toUpperCase() + tab.slice(1)}
                </button>
              ))}
            </div>

            <div className="pt-3">
              {activeTab === "position" && (
                <div>
                  <TransformControlGroup
                    label="X"
                    value={selectedObject.position.x}
                    onChange={(axis, val) =>
                      updateTransform("position", "x", val)
                    }
                    axis="x"
                    min={-20}
                    max={20}
                  />
                  <TransformControlGroup
                    label="Y"
                    value={selectedObject.position.y}
                    onChange={(axis, val) =>
                      updateTransform("position", "y", val)
                    }
                    axis="y"
                    min={-20}
                    max={20}
                  />
                  <TransformControlGroup
                    label="Z"
                    value={selectedObject.position.z}
                    onChange={(axis, val) =>
                      updateTransform("position", "z", val)
                    }
                    axis="z"
                    min={-20}
                    max={20}
                  />
                </div>
              )}

              {activeTab === "rotation" && (
                <div>
                  <TransformControlGroup
                    label="X"
                    value={selectedObject.rotation.x}
                    onChange={(axis, val) =>
                      updateTransform("rotation", "x", val)
                    }
                    axis="x"
                    min={-Math.PI}
                    max={Math.PI}
                  />
                  <TransformControlGroup
                    label="Y"
                    value={selectedObject.rotation.y}
                    onChange={(axis, val) =>
                      updateTransform("rotation", "y", val)
                    }
                    axis="y"
                    min={-Math.PI}
                    max={Math.PI}
                  />
                  <TransformControlGroup
                    label="Z"
                    value={selectedObject.rotation.z}
                    onChange={(axis, val) =>
                      updateTransform("rotation", "z", val)
                    }
                    axis="z"
                    min={-Math.PI}
                    max={Math.PI}
                  />
                </div>
              )}

              {activeTab === "scale" && (
                <div>
                  <div className="mb-2">
                    <label className="flex items-center text-[#FDE9CE] text-xs md:text-sm">
                      <input
                        type="checkbox"
                        checked={uniformScale}
                        onChange={() => setUniformScale(!uniformScale)}
                        className="mr-2 accent-blue-500"
                      />
                      Uniform Scale
                    </label>
                  </div>

                  <TransformControlGroup
                    label="X"
                    value={selectedObject.scale.x}
                    onChange={(axis, val) => updateScale("x", val)}
                    axis="x"
                    min={0.1}
                    max={5}
                  />
                  <TransformControlGroup
                    label="Y"
                    value={selectedObject.scale.y}
                    onChange={(axis, val) => updateScale("y", val)}
                    axis="y"
                    disabled={uniformScale}
                    min={0.1}
                    max={5}
                  />
                  <TransformControlGroup
                    label="Z"
                    value={selectedObject.scale.z}
                    onChange={(axis, val) => updateScale("z", val)}
                    axis="z"
                    disabled={uniformScale}
                    min={0.1}
                    max={5}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Reset button at bottom with Lucide icon */}
          <button
            onClick={resetTransform}
            className="w-full p-2 bg-[#3A3D4A] text-[#FDE9CE] rounded hover:bg-[#4A4E5A] flex items-center justify-center"
          >
            <RotateCcw size={16} className="mr-2" />
            Reset Transform
          </button>
        </>
      )}
    </div>
  );
}
