"use client";

import { useState, useCallback } from "react";
import { EnvironmentPreviewBall } from "./EnvironmentPreviewBall";
import { useHistoryDebounce } from "../hooks/useDebounce";

export function EnvironmentPanel({
  envPreset,
  setEnvPreset,
  bgColor,
  setBgColor,
  showEnvironment,
  setShowEnvironment,
  customHdri,
  setCustomHdri,
  envIntensity,
  setEnvIntensity,
  showModelStats,
  setShowModelStats,
  envBlur,
  setEnvBlur,
  envRotation,
  setEnvRotation,
  addToHistory,
}) {
  const [activeTab, setActiveTab] = useState("presets");

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    300
  );

  const handleHdriUpload = useCallback(
    (event) => {
      const file = event.target.files[0];
      if (file) {
        const url = URL.createObjectURL(file);
        setCustomHdri(url);
        setEnvPreset("custom");
        // Add to history for HDRI upload
        if (addToHistory) {
          addToHistoryImmediate();
        }
      }
    },
    [setCustomHdri, setEnvPreset, addToHistoryImmediate, addToHistory]
  );

  const presets = [
    { value: "hdri_15", label: "HDRI 15" },
    { value: "hdri_metal", label: "HDRI Metal" },
    { value: "sunset", label: "Sunset" },
    { value: "dawn", label: "Dawn" },
    { value: "night", label: "Night" },
    { value: "warehouse", label: "Warehouse" },
    { value: "forest", label: "Forest" },
    { value: "apartment", label: "Apartment" },
    { value: "studio", label: "Studio" },
    { value: "studio_small", label: "Studio Small" },
    { value: "city", label: "City" },
    { value: "park", label: "Park" },
    { value: "lobby", label: "Lobby" },
    { value: "none", label: "None" },
  ];

  const renderSlider = (label, value, onChange, min, max, step, unit = "") => (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-1">
        <label className="text-sm font-medium text-gray-200">{label}</label>
        <span className="text-xs text-gray-400">
          {value.toFixed(2)}
          {unit}
        </span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => {
          onChange(e);
          if (addToHistory) {
            addToHistoryDebounced();
          }
        }}
        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-500"
      />
    </div>
  );

  return (
    <div className="text-white w-full overflow-y-auto text-xs">
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-4 text-gray-100">
          Environment
        </h2>

        {/* Tab Navigation */}
        <div className="flex gap-1 mb-4 bg-gray-800/30 p-0.5 rounded-md">
          <button
            onClick={() => setActiveTab("presets")}
            className={`flex-1 px-2.5 py-1.5 rounded text-xs font-medium transition-all duration-200 ${
              activeTab === "presets"
                ? "bg-blue-500/90 text-white shadow-sm"
                : "text-gray-300 hover:bg-gray-700/50"
            }`}
          >
            Presets
          </button>
          <button
            onClick={() => setActiveTab("custom")}
            className={`flex-1 px-2.5 py-1.5 rounded text-xs font-medium transition-all duration-200 ${
              activeTab === "custom"
                ? "bg-blue-500/90 text-white shadow-sm"
                : "text-gray-300 hover:bg-gray-700/50"
            }`}
          >
            Custom
          </button>
          <button
            onClick={() => setActiveTab("settings")}
            className={`flex-1 px-2.5 py-1.5 rounded text-xs font-medium transition-all duration-200 ${
              activeTab === "settings"
                ? "bg-blue-500/90 text-white shadow-sm"
                : "text-gray-300 hover:bg-gray-700/50"
            }`}
          >
            Settings
          </button>
        </div>

        {/* Presets Tab */}
        {activeTab === "presets" && (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-3">
              {presets.map((preset) => (
                <button
                  key={preset.value}
                  onClick={() => {
                    setEnvPreset(preset.value);
                    if (addToHistory) {
                      addToHistoryImmediate();
                    }
                  }}
                  className={`group relative flex flex-col items-center p-2 rounded-lg transition-all duration-200 ${
                    envPreset === preset.value
                      ? "bg-blue-500/20 ring-2 ring-blue-500"
                      : "bg-gray-800/50 hover:bg-gray-700/50"
                  }`}
                >
                  {preset.value !== "none" ? (
                    <EnvironmentPreviewBall
                      preset={preset.value}
                      intensity={envIntensity}
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-lg bg-gray-800 flex items-center justify-center text-[10px]">
                      None
                    </div>
                  )}
                  <span className="mt-2 text-xs font-medium text-gray-200">
                    {preset.label}
                  </span>
                  {envPreset === preset.value && (
                    <div className="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full" />
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Custom HDRI Tab */}
        {activeTab === "custom" && (
          <div className="space-y-4">
            <div className="bg-gray-800/50 rounded-lg p-4">
              <input
                type="file"
                accept=".hdr,.exr,.png,.jpg,.jpeg"
                onChange={handleHdriUpload}
                style={{ display: "none" }}
                id="hdri-upload"
              />
              <label
                htmlFor="hdri-upload"
                className="flex items-center justify-center gap-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg cursor-pointer transition-colors duration-200"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Upload HDRI
              </label>
              {customHdri && (
                <div className="mt-4 flex flex-col items-center">
                  <EnvironmentPreviewBall
                    preset="custom"
                    customHdri={customHdri}
                    intensity={envIntensity}
                  />
                  <div className="mt-2 text-sm text-green-400 flex items-center gap-1">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Custom HDRI loaded
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <div className="space-y-6">
            {/* Environment Display Settings */}
            <div className="bg-gray-800/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-200 mb-4">
                Display Settings
              </h3>
              <div className="space-y-4">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showEnvironment}
                    onChange={(e) => {
                      setShowEnvironment(e.target.checked);
                      if (addToHistory) {
                        addToHistoryImmediate();
                      }
                    }}
                    className="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-200">
                    Show as Background
                  </span>
                </label>

                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Background Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={bgColor}
                      onChange={(e) => {
                        setBgColor(e.target.value);
                        if (addToHistory) {
                          addToHistoryImmediate();
                        }
                      }}
                      className="w-10 h-10 rounded cursor-pointer"
                    />
                    <span className="text-xs text-gray-400">{bgColor}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Environment Adjustment Settings */}
            <div className="bg-gray-800/50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-200 mb-4">
                Environment Adjustments
              </h3>
              <div className="space-y-4">
                {renderSlider(
                  "Environment Intensity",
                  envIntensity,
                  (e) => setEnvIntensity(parseFloat(e.target.value)),
                  0,
                  10,
                  0.01
                )}

                {renderSlider(
                  "Environment Rotation",
                  envRotation,
                  (e) => {
                    const newValue = Number(e.target.value);
                    setEnvRotation(
                      newValue >= 0 ? newValue % 360 : 360 + (newValue % 360)
                    );
                  },
                  0,
                  Math.PI * 2,
                  0.01,
                  "°"
                )}

                {renderSlider(
                  "Background Blur",
                  envBlur,
                  (e) => setEnvBlur(parseFloat(e.target.value)),
                  0,
                  1,
                  0.01
                )}
              </div>
            </div>

            {/* Model Stats Toggle */}
            <div className="bg-gray-800/50 rounded-lg p-4">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showModelStats}
                  onChange={(e) => {
                    setShowModelStats(e.target.checked);
                    if (addToHistory) {
                      addToHistoryImmediate();
                    }
                  }}
                  className="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-200">Show Model Stats</span>
              </label>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
