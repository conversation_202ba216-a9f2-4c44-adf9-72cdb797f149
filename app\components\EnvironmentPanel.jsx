"use client";

import { useState, useCallback, useEffect } from "react";
import { ChromePicker } from "react-color";
import {
  EnvironmentPreviewBall,
  SingleEnvironmentPreview,
} from "./EnvironmentPreviewBall";
import { Grid3X3, List } from "lucide-react";
import { useHistoryDebounce } from "../hooks/useDebounce";

export function EnvironmentPanel({
  envPreset,
  setEnvPreset,
  bgColor,
  setBgColor,
  showEnvironment,
  setShowEnvironment,
  customHdri,
  setCustomHdri,
  envIntensity,
  setEnvIntensity,
  showModelStats,
  setShowModelStats,
  envBlur,
  setEnvBlur,
  envRotation,
  setEnvRotation,
  addToHistory,
}) {
  const [activeTab, setActiveTab] = useState("presets");
  const [viewMode, setViewMode] = useState("single"); // "single" or "grid"
  const [isMobile, setIsMobile] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [displayBgColorPicker, setDisplayBgColorPicker] = useState(false);
  const itemsPerPage = 6;

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Force single view on mobile
  useEffect(() => {
    if (isMobile) {
      setViewMode("single");
    }
  }, [isMobile]);

  // Reset to first page when switching tabs or view modes
  useEffect(() => {
    setCurrentPage(0);
  }, [activeTab, viewMode]);

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    300
  );

  const handleHdriUpload = useCallback(
    (event) => {
      const file = event.target.files[0];
      if (file) {
        const url = URL.createObjectURL(file);
        setCustomHdri(url);
        setEnvPreset("custom");
        // Add to history for HDRI upload
        if (addToHistory) {
          addToHistoryImmediate();
        }
      }
    },
    [setCustomHdri, setEnvPreset, addToHistoryImmediate, addToHistory]
  );

  const presets = [
    { value: "hdri_15", label: "Metal 1" },
    { value: "hdri_metal", label: "Metal 2" },
    { value: "hdri_metal2", label: "Metal 3" },
    { value: "hdri_metal3", label: "Metal 4" },
    { value: "sunset", label: "Sunset" },
    { value: "dawn", label: "Dawn" },
    { value: "night", label: "Night" },
    { value: "warehouse", label: "Warehouse" },
    { value: "forest", label: "Forest" },
    { value: "apartment", label: "Apartment" },
    { value: "studio", label: "Studio" },
    { value: "studio_small", label: "Studio Small" },
    { value: "city", label: "City" },
    { value: "none", label: "None" },
  ];

  // Pagination logic
  const totalPages = Math.ceil(presets.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPresets = presets.slice(startIndex, endIndex);

  const goToPage = (page) => {
    setCurrentPage(Math.max(0, Math.min(page, totalPages - 1)));
  };

  const goToNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleBgColorClick = () => {
    setDisplayBgColorPicker(!displayBgColorPicker);
  };

  const handleBgColorClose = () => {
    setDisplayBgColorPicker(false);
  };

  const handleBgColorChange = (color) => {
    const hexColor = color.hex;
    setBgColor(hexColor);
  };

  const renderSlider = (label, value, onChange, min, max, step, unit = "") => (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-1">
        <label className="text-xs md:text-sm font-medium text-gray-200">
          {label}
        </label>
        <span className="text-[10px] md:text-xs text-gray-400">
          {value.toFixed(2)}
          {unit}
        </span>
      </div>
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => {
          onChange(e);
          if (addToHistory) {
            addToHistoryDebounced();
          }
        }}
        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-500"
      />
    </div>
  );

  return (
    <div className="text-white w-full overflow-y-auto text-[10px] md:text-xs no-scrollbar">
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-4 text-gray-100">
          Environment
        </h2>

        {/* Tab Navigation */}
        <div className="flex gap-1 mb-4 bg-gray-800/30 p-0.5 rounded-md">
          <button
            onClick={() => setActiveTab("presets")}
            className={`flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 ${
              activeTab === "presets"
                ? "bg-blue-500/90 text-white shadow-sm"
                : "text-gray-300 hover:bg-gray-700/50"
            }`}
          >
            Presets
          </button>
          {/* <button
            onClick={() => setActiveTab("custom")}
            className={`flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 ${
              activeTab === "custom"
                ? "bg-blue-500/90 text-white shadow-sm"
                : "text-gray-300 hover:bg-gray-700/50"
            }`}
          >
            Custom
          </button> */}
          <button
            onClick={() => setActiveTab("settings")}
            className={`flex-1 px-2.5 py-1.5 rounded text-[10px] md:text-xs font-medium transition-all duration-200 ${
              activeTab === "settings"
                ? "bg-blue-500/90 text-white shadow-sm"
                : "text-gray-300 hover:bg-gray-700/50"
            }`}
          >
            Settings
          </button>
        </div>

        {/* Presets Tab */}
        {activeTab === "presets" && (
          <div className="space-y-4">
            {/* View Mode Toggle - Only show on larger screens */}
            {!isMobile && (
              <div className="flex justify-end items-center mb-4">
                <div className="flex bg-gray-800/50 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode("single")}
                    className={`p-2 rounded transition-all duration-200 relative group ${
                      viewMode === "single"
                        ? "bg-blue-500 text-white"
                        : "text-gray-300 hover:text-white hover:bg-gray-700/50"
                    }`}
                    title="Single Preview - Shows one large preview with list of preset names"
                  >
                    <List className="w-4 h-4" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-[10px] md:text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                      Single Preview
                    </div>
                  </button>
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded transition-all duration-200 relative group ${
                      viewMode === "grid"
                        ? "bg-blue-500 text-white"
                        : "text-gray-300 hover:text-white hover:bg-gray-700/50"
                    }`}
                    title="Grid Preview - Shows all presets as small preview balls"
                  >
                    <Grid3X3 className="w-4 h-4" />
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-900 text-white text-[10px] md:text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                      Grid Preview
                    </div>
                  </button>
                </div>
              </div>
            )}

            {/* Single Preview Mode */}
            {viewMode === "single" && (
              <div className="space-y-3">
                {/* Single Preview Display */}
                <div className="mb-3">
                  <SingleEnvironmentPreview
                    preset={envPreset}
                    customHdri={customHdri}
                    intensity={envIntensity}
                  />
                </div>

                {/* Preset Selection List */}
                <div className="space-y-1.5">
                  {currentPresets.map((preset) => (
                    <button
                      key={preset.value}
                      onClick={() => {
                        setEnvPreset(preset.value);
                        if (addToHistory) {
                          addToHistoryImmediate();
                        }
                      }}
                      className={`w-full flex items-center justify-between p-2 rounded-md transition-all duration-200 ${
                        envPreset === preset.value
                          ? "bg-blue-500/20 ring-1 ring-blue-500"
                          : "bg-gray-800/50 hover:bg-gray-700/50"
                      }`}
                    >
                      <span className="text-[10px] md:text-xs font-medium text-gray-200">
                        {preset.label}
                      </span>
                      {envPreset === preset.value && (
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                      )}
                    </button>
                  ))}
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-700/50">
                    <button
                      onClick={goToPrevPage}
                      disabled={currentPage === 0}
                      className={`flex items-center gap-1 px-2 py-1 rounded text-[10px] md:text-xs transition-all duration-200 ${
                        currentPage === 0
                          ? "text-gray-500 cursor-not-allowed"
                          : "text-gray-300 hover:text-white hover:bg-gray-700/50"
                      }`}
                    >
                      <svg
                        className="w-3 h-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 19l-7-7 7-7"
                        />
                      </svg>
                      Prev
                    </button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: totalPages }, (_, i) => (
                        <button
                          key={i}
                          onClick={() => goToPage(i)}
                          className={`w-2 h-2 rounded-full transition-all duration-200 ${
                            currentPage === i
                              ? "bg-blue-500"
                              : "bg-gray-600 hover:bg-gray-500"
                          }`}
                        />
                      ))}
                    </div>

                    <button
                      onClick={goToNextPage}
                      disabled={currentPage === totalPages - 1}
                      className={`flex items-center gap-1 px-2 py-1 rounded text-[10px] md:text-xs transition-all duration-200 ${
                        currentPage === totalPages - 1
                          ? "text-gray-500 cursor-not-allowed"
                          : "text-gray-300 hover:text-white hover:bg-gray-700/50"
                      }`}
                    >
                      Next
                      <svg
                        className="w-3 h-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Grid Preview Mode */}
            {viewMode === "grid" && !isMobile && (
              <div className="grid grid-cols-3 gap-3">
                {presets.map((preset) => (
                  <button
                    key={preset.value}
                    onClick={() => {
                      setEnvPreset(preset.value);
                      if (addToHistory) {
                        addToHistoryImmediate();
                      }
                    }}
                    className={`group relative flex flex-col items-center p-2 rounded-lg transition-all duration-200 ${
                      envPreset === preset.value
                        ? "bg-blue-500/20 ring-2 ring-blue-500"
                        : "bg-gray-800/50 hover:bg-gray-700/50"
                    }`}
                  >
                    {preset.value !== "none" ? (
                      <EnvironmentPreviewBall
                        preset={preset.value}
                        intensity={envIntensity}
                      />
                    ) : (
                      <div className="w-16 h-16 rounded-lg bg-gray-800 flex items-center justify-center text-[10px]">
                        None
                      </div>
                    )}
                    <span className="mt-2 text-[10px] md:text-xs font-medium text-gray-200">
                      {preset.label}
                    </span>
                    {envPreset === preset.value && (
                      <div className="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full" />
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Custom HDRI Tab */}
        {/* {activeTab === "custom" && (
          <div className="space-y-4">
            <div className="bg-gray-800/50 rounded-lg p-4">
              <input
                type="file"
                accept=".hdr,.exr,.png,.jpg,.jpeg"
                onChange={handleHdriUpload}
                style={{ display: "none" }}
                id="hdri-upload"
              />
              <label
                htmlFor="hdri-upload"
                className="flex items-center justify-center gap-2 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg cursor-pointer transition-colors duration-200"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                Upload HDRI
              </label>
              {customHdri && (
                <div className="mt-4 flex flex-col items-center">
                  <SingleEnvironmentPreview
                    preset="custom"
                    customHdri={customHdri}
                    intensity={envIntensity}
                  />
                  <div className="mt-2 text-xs md:text-sm text-green-400 flex items-center gap-1">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Custom HDRI loaded
                  </div>
                </div>
              )}
            </div>
          </div>
        )} */}

        {/* Settings Tab */}
        {activeTab === "settings" && (
          <div className="space-y-6">
            {/* Environment Display Settings */}
            <div className="bg-gray-800/50 rounded-lg p-1 md:p-4">
              <h3 className="text-xs md:text-sm font-medium text-gray-200 mb-4">
                Display Settings
              </h3>
              <div className="space-y-4">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showEnvironment}
                    onChange={(e) => {
                      setShowEnvironment(e.target.checked);
                      if (addToHistory) {
                        addToHistoryImmediate();
                      }
                    }}
                    className="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500"
                  />
                  <span className="text-xs md:text-sm text-gray-200">
                    Show as Background
                  </span>
                </label>

                <div>
                  <label className="block text-xs md:text-sm font-medium text-gray-200 mb-2">
                    Background Color
                  </label>
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div
                        className="w-10 h-10 rounded cursor-pointer border border-gray-600"
                        style={{ backgroundColor: bgColor }}
                        onClick={handleBgColorClick}
                      />
                      {displayBgColorPicker && (
                        <div style={{ position: "absolute", zIndex: "2" }}>
                          <div
                            style={{
                              position: "fixed",
                              top: "0px",
                              right: "0px",
                              bottom: "0px",
                              left: "0px",
                            }}
                            onClick={handleBgColorClose}
                          />
                          <ChromePicker
                            color={bgColor}
                            onChange={handleBgColorChange}
                            disableAlpha
                          />
                        </div>
                      )}
                    </div>
                    <input
                      type="text"
                      value={bgColor}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Allow typing and validate hex format
                        if (
                          value.match(/^#[0-9A-Fa-f]{0,6}$/) ||
                          value === ""
                        ) {
                          setBgColor(value);
                          if (addToHistory) {
                            addToHistoryImmediate();
                          }
                        }
                      }}
                      placeholder="#ffffff"
                      className="px-2 py-1 bg-gray-700 border border-gray-600 rounded text-[10px] md:text-xs text-gray-200 focus:border-blue-500 focus:outline-none w-20"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Environment Adjustment Settings */}
            <div className="bg-gray-800/50 rounded-lg p-1 md:p-4">
              <h3 className="text-xs md:text-sm font-medium text-gray-200 mb-4">
                Environment Adjustments
              </h3>
              <div className="space-y-4">
                {renderSlider(
                  "Environment Intensity",
                  envIntensity,
                  (e) => setEnvIntensity(parseFloat(e.target.value)),
                  0,
                  10,
                  0.01
                )}

                {renderSlider(
                  "Environment Rotation",
                  envRotation,
                  (e) => {
                    const newValue = Number(e.target.value);
                    setEnvRotation(
                      newValue >= 0 ? newValue % 360 : 360 + (newValue % 360)
                    );
                  },
                  0,
                  Math.PI * 2,
                  0.01,
                  "°"
                )}

                {renderSlider(
                  "Background Blur",
                  envBlur,
                  (e) => setEnvBlur(parseFloat(e.target.value)),
                  0,
                  1,
                  0.01
                )}
              </div>
            </div>

            {/* Model Stats Toggle */}
            <div className="bg-gray-800/50 rounded-lg p-4">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showModelStats}
                  onChange={(e) => {
                    setShowModelStats(e.target.checked);
                    if (addToHistory) {
                      addToHistoryImmediate();
                    }
                  }}
                  className="form-checkbox h-4 w-4 text-blue-500 rounded border-gray-600 focus:ring-blue-500"
                />
                <span className="text-xs md:text-sm text-gray-200">
                  Show Model Stats
                </span>
              </label>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
