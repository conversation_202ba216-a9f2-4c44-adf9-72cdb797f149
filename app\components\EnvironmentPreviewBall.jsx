"use client";

import { Canvas } from "@react-three/fiber";
import { Environment, OrbitControls, Sphere } from "@react-three/drei";
import * as THREE from "three";
import { useRef, useEffect, useState } from "react";

// Single preview ball component
function PreviewBall({ position, preset, customHdri, intensity = 1 }) {
  // Calculate the effective intensity based on preset
  const effectiveIntensity = preset === "hdri_15" ? intensity : 1;

  return (
    <group position={position}>
      <Sphere args={[1, 32, 32]}>
        <meshStandardMaterial
          metalness={0.9}
          roughness={0.1}
          color={new THREE.Color("#ffffff")}
        />
      </Sphere>

      {preset === "custom" && customHdri ? (
        <Environment
          files={customHdri}
          background={false}
          environmentIntensity={intensity}
        />
      ) : preset === "hdri_15" ? (
        <Environment
          files="/hdris/hdri_15.hdr"
          background={false}
          environmentIntensity={effectiveIntensity}
        />
      ) : preset === "hdri_metal" ? (
        <Environment
          files="/hdris/hdri_metal.exr"
          background={false}
          environmentIntensity={1}
        />
      ) : preset === "hdri_metal2" ? (
        <Environment
          files="/hdris/metal_hdri2.hdr"
          background={false}
          environmentIntensity={1}
        />
      ) : preset === "hdri_metal3" ? (
        <Environment
          files="/hdris/metal_hdri3.hdr"
          background={false}
          environmentIntensity={1}
        />
      ) : preset === "studio_small" ? (
        <Environment
          files="/hdris/studio_small_02_2k.hdr"
          background={false}
          environmentIntensity={1}
        />
      ) : preset !== "none" ? (
        <Environment
          preset={preset}
          background={false}
          environmentIntensity={1}
        />
      ) : null}
    </group>
  );
}

// Single preview component for mobile and list view
export function SingleEnvironmentPreview({
  preset,
  customHdri,
  intensity = 1,
}) {
  const canvasRef = useRef(null);

  return (
    <div className="w-full h-48 rounded-lg overflow-hidden bg-gray-800/50">
      <Canvas ref={canvasRef} camera={{ position: [0, 0, 2.5] }} dpr={[1, 1.5]}>
        <PreviewBall
          position={[0, 0, 0]}
          preset={preset}
          customHdri={customHdri}
          intensity={intensity}
        />
        <OrbitControls enableZoom={false} enablePan={false} />
      </Canvas>
    </div>
  );
}

// Grid preview component for desktop
export function GridEnvironmentPreview({
  presets,
  selectedPreset,
  onPresetSelect,
  intensity = 1,
}) {
  const canvasRef = useRef(null);

  return (
    <div className="w-full rounded-lg overflow-hidden">
      <Canvas ref={canvasRef} camera={{ position: [0, 0, 2.5] }} dpr={[1, 1.5]}>
        {presets.map((preset, index) => (
          <PreviewBall
            key={preset.value}
            position={[index * 2.5 - (presets.length - 1) * 1.25, 0, 0]}
            preset={preset.value}
            intensity={intensity}
          />
        ))}
        <OrbitControls enableZoom={false} enablePan={false} />
      </Canvas>
    </div>
  );
}

// Main preview component that renders all balls in one canvas (legacy)
export function EnvironmentPreviewBall({ preset, customHdri, intensity = 1 }) {
  const canvasRef = useRef(null);

  return (
    <div className="w-16 h-16 rounded-lg overflow-hidden">
      <Canvas ref={canvasRef} camera={{ position: [0, 0, 2.5] }} dpr={[1, 1.5]}>
        <PreviewBall
          position={[0, 0, 0]}
          preset={preset}
          customHdri={customHdri}
          intensity={intensity}
        />
        <OrbitControls enableZoom={false} enablePan={false} />
      </Canvas>
    </div>
  );
}
