"use client";

import { Canvas } from "@react-three/fiber";
import { Environment, OrbitControls, Sphere } from "@react-three/drei";
import * as THREE from "three";

export function EnvironmentPreviewBall({ preset, customHdri, intensity = 1 }) {
  return (
    <div className="w-16 h-16 rounded-lg overflow-hidden">
      <Canvas camera={{ position: [0, 0, 2.5] }}>
        <Sphere args={[1, 32, 32]}>
          <meshStandardMaterial
            metalness={0.9}
            roughness={0.1}
            color={new THREE.Color("#ffffff")}
          />
        </Sphere>

        {preset === "custom" && customHdri ? (
          <Environment
            files={customHdri}
            background={false}
            environmentIntensity={intensity}
          />
        ) : preset === "hdri_15" ? (
          <Environment
            files="/hdris/hdri_15.hdr"
            background={false}
            environmentIntensity={intensity}
          />
        ) : preset === "hdri_metal" ? (
          <Environment
            files="/hdris/hdri_metal.exr"
            background={false}
            environmentIntensity={intensity}
          />
        ) : preset === "studio_small" ? (
          <Environment
            files="/hdris/studio_small_02_2k.hdr"
            background={false}
            environmentIntensity={intensity}
          />
        ) : (
          <Environment
            preset={preset}
            background={false}
            environmentIntensity={intensity}
          />
        )}

        <OrbitControls enableZoom={false} enablePan={false} />
      </Canvas>
    </div>
  );
}
