import { ShaderMaterial, Vector3, Vector2, Matrix4 } from "three";
import * as DiamondTracer from "./DiamondTracer";

export class DiamondMaterial {
  constructor({ cubeAPI, it, colorEnvMap, geo, camera, onLoop = () => {} }) {
    let center = new Vector3();
    geo = geo.clone();
    geo.computeBoundingBox();
    geo.computeBoundingSphere();
    geo.boundingBox.getCenter(center);

    let shader = new ShaderMaterial({
      transparent: true,
      uniforms: {
        offsetRainbowRedChannelNoise: { value: new Vector3(0, 0, 0) },
        offsetRainbowGreenChannelNoise: { value: new Vector3(0, 0, 0) },
        offsetRainbowBlueChannelNoise: { value: new Vector3(0, 0, 0) },

        roughness: { value: 0.2955779116254787 },
        bounces: { value: 4 },
        ior: { value: 2.4 },
        colorEnvMapRotY: { value: Math.PI * 0.25 },
        modelMatrix: { value: it.matrix.clone() },
        diamondOriginCenter: {
          value: center.clone(),
        },
        contrastFactor: { value: 1 },
        brightnessFactor: { value: 1 },
        diamondOriginRadius: { value: 0 },
        aberrationStrength: { value: 0.0025 },
        // chromaticAberration: { value: 0.0025 },
        normalEnvMap: { value: cubeAPI.texture },
        colorEnvMap: { value: colorEnvMap },
        matrixWorld: { value: new Matrix4() },
        cameraMatrixWorld: { value: camera.matrixWorld },
        resolution: {
          value: new Vector2(
            window.innerWidth,
            window.innerHeight
          ).multiplyScalar(window.devicePixelRatio),
        },
      },
      vertexShader: DiamondTracer.vertexShader,
      fragmentShader: DiamondTracer.fragmentShader,
    });

    onLoop(() => {
      if (cubeAPI) {
        camera.updateMatrixWorld();
        shader.uniforms.modelMatrix.value.copy(it.matrix);
        shader.uniforms.cameraMatrixWorld.value = camera.matrixWorld;
        shader.uniforms.diamondOriginRadius.value = geo.boundingSphere.radius; //
        shader.uniforms.normalEnvMap.value = cubeAPI.texture;
      }
    });

    window.addEventListener("keydown", () => {
      console.log(shader.uniforms.roughness.value);
    });

    shader.clean = () => {
      shader.dispose();
    };

    return shader;
  }
}
