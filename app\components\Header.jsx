import {
  CloudUpload,
  Share,
  Store,
  Bell,
  RotateCcw,
  Camera,
  Upload,
  Maximize2,
  Minimize2,
  RotateCw,
} from "lucide-react";
import Image from "next/image";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/app/components/ui/tooltip";
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
} from "@/app/components/ui/avatar";
import { useState } from "react";
import { SaveSceneModal } from "./SaveSceneModal";
import { LoadSceneModal } from "./LoadSceneModal";

export default function Header({
  onReset,
  onScreenshot,
  onSaveScene,
  onLoadScene,
  isFullscreen,
  onToggleFullscreen,
  onUndo,
  onRedo,
}) {
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);

  return (
    <>
      <header
        id="header"
        className={`fixed w-full z-50 px-4 py-2 transition-all duration-300 ${
          isFullscreen ? "opacity-0 hover:opacity-100" : "opacity-100"
        }`}
      >
        <div className="rounded-full px-6 py-1 flex items-center justify-between">
          <div className="w-auto h-10">
            <a
              href="https://agape-cms-library.vercel.app/"
              className="flex items-center h-full"
            >
              <Image
                src="/Agape_logo.png"
                alt="Agape"
                width={1024}
                height={800}
                className="h-full w-auto object-contain invert"
              />
            </a>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <HeaderButton
                icon={isFullscreen ? Minimize2 : Maximize2}
                tooltip={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}
                onClick={onToggleFullscreen}
              />
            </TooltipProvider>
            <div className="h-6 w-px bg-white/20 mx-1" />
            <TooltipProvider>
              <HeaderButton
                icon={RotateCcw}
                tooltip="Undo (Ctrl+Z)"
                onClick={onUndo}
              />
            </TooltipProvider>
            <TooltipProvider>
              <HeaderButton
                icon={RotateCw}
                tooltip="Redo (Ctrl+Y)"
                onClick={onRedo}
              />
            </TooltipProvider>
            <div className="h-6 w-px bg-white/20 mx-1" />
            <TooltipProvider>
              <HeaderButton
                icon={RotateCcw}
                tooltip="Reset Scene"
                onClick={onReset}
              />
            </TooltipProvider>
            <TooltipProvider>
              <HeaderButton
                icon={Camera}
                tooltip="Screenshot"
                onClick={onScreenshot}
              />
            </TooltipProvider>
            <div className="h-6 w-px bg-white/20 mx-1" />
            <div className="flex items-center gap-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => setShowLoadModal(true)}
                      className="px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group"
                    >
                      <Upload className="w-4 h-4 text-white/80 group-hover:text-white" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Load Scene</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => setShowSaveModal(true)}
                      className="px-2 py-1.5 rounded-full backdrop-blur-sm bg-white/10 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-200 flex items-center gap-1.5 group"
                    >
                      <CloudUpload className="w-4 h-4 text-white/80 group-hover:text-white" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Save Scene</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </div>
      </header>

      <SaveSceneModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        onSave={(sceneName, saveType) => {
          onSaveScene(sceneName, saveType);
          setShowSaveModal(false);
        }}
      />

      <LoadSceneModal
        isOpen={showLoadModal}
        onClose={() => setShowLoadModal(false)}
        onLoad={(config) => {
          onLoadScene(config);
          setShowLoadModal(false);
        }}
      />
    </>
  );
}

function HeaderButton({ icon: Icon, tooltip, primary = false, onClick }) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          onClick={onClick}
          className={`p-1.5 rounded-full ${
            primary
              ? "bg-primary text-white hover:bg-primary-600"
              : "text-white hover:bg-white/20"
          }`}
        >
          <Icon className="w-4 h-4" />
        </button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{tooltip}</p>
      </TooltipContent>
    </Tooltip>
  );
}
