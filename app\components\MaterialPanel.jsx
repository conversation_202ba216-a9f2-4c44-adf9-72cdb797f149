"use client";

import { useState, useEffect } from "react";
import * as THREE from "three";
import { useHistoryDebounce } from "../hooks/useDebounce";

// Define material presets
const MATERIAL_PRESETS = {
  gems: [
    {
      name: "<PERSON>",
      color: "#ffffff",
      roughness: 0.05,
      metalness: 0.1,
      clearcoat: 1.0,
      clearcoatRoughness: 0.02,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 1.5,
    },
    {
      name: "Emerald",
      color: "#22dfa3",
      roughness: 0.1,
      metalness: 0.0,
      clearcoat: 0.8,
      clearcoatRoughness: 0.05,
      transparent: true,
      opacity: 0.85,
      envMapIntensity: 1.2,
    },
    {
      name: "Ruby",
      color: "#e14276",
      roughness: 0.08,
      metalness: 0.0,
      clearcoat: 0.9,
      clearcoatRoughness: 0.03,
      transparent: true,
      opacity: 0.9,
      envMapIntensity: 1.3,
    },
  ],
  metals: [
    {
      name: "Platinum",
      color: "#e5e4e2",
      roughness: 0.15,
      metalness: 1.0,
      clearcoat: 0.3,
      clearcoatRoughness: 0.1,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 1.0,
    },
    {
      name: "Yellow Gold",
      color: "#E5b377",
      roughness: 0.12,
      metalness: 1.0,
      clearcoat: 0.2,
      clearcoatRoughness: 0.08,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 0.9,
    },
    {
      name: "Rose Gold",
      color: "#e8b4a0",
      roughness: 0.14,
      metalness: 1.0,
      clearcoat: 0.25,
      clearcoatRoughness: 0.09,
      transparent: false,
      opacity: 1.0,
      envMapIntensity: 0.95,
    },
  ],
};

export function MaterialPanel({
  selectedObjects,
  forceUpdate,
  setMaterialVersion,
  addToHistory,
}) {
  const [materialType, setMaterialType] = useState("standard");
  const [materialProps, setMaterialProps] = useState({
    color: "#ffffff",
    roughness: 0.5,
    metalness: 0.5,
    clearcoat: 0.5,
    clearcoatRoughness: 0.5,
    wireframe: false,
    transparent: false,
    opacity: 1.0,
  });

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    300
  );

  // Update local state when selected objects change
  useEffect(() => {
    // Ensure selectedObjects is always an array
    const objectsArray = Array.isArray(selectedObjects)
      ? selectedObjects
      : [selectedObjects].filter(Boolean);

    if (objectsArray.length === 0) return;

    // Use the first selected object to determine material type and properties
    const obj = objectsArray[0];
    if (!obj.material) return;

    // Determine material type
    let type = "standard";
    if (obj.material.type === "MeshBasicMaterial") type = "basic";
    else if (obj.material.type === "MeshStandardMaterial") type = "standard";
    else if (obj.material.type === "MeshPhysicalMaterial") type = "physical";
    else if (obj.material.type === "MeshToonMaterial") type = "toon";
    else if (obj.material.type === "MeshNormalMaterial") type = "normal";

    setMaterialType(type);

    // Update material properties
    const props = {
      color: obj.material.color
        ? "#" + obj.material.color.getHexString()
        : "#ffffff",
      roughness:
        obj.material.roughness !== undefined ? obj.material.roughness : 0.5,
      metalness:
        obj.material.metalness !== undefined ? obj.material.metalness : 0.5,
      clearcoat:
        obj.material.clearcoat !== undefined ? obj.material.clearcoat : 0.5,
      clearcoatRoughness:
        obj.material.clearcoatRoughness !== undefined
          ? obj.material.clearcoatRoughness
          : 0.5,
      wireframe: obj.material.wireframe || false,
      transparent: obj.material.transparent || false,
      opacity: obj.material.opacity !== undefined ? obj.material.opacity : 1.0,
    };

    setMaterialProps(props);
  }, [selectedObjects]);

  if (selectedObjects.length === 0) {
    return (
      <div className="p-2 text-center text-gray-600">
        Select an object to edit its material
      </div>
    );
  }

  const applyPreset = (preset) => {
    // Update local state
    setMaterialProps(preset);

    // Update all selected objects by modifying their existing material properties
    selectedObjects.forEach((obj) => {
      if (!obj || !obj.material) return;

      const material = obj.material;

      // Update color if the material has a color property
      if (material.color) {
        material.color.set(preset.color);
      }

      // Update properties that exist on the material
      if (material.roughness !== undefined) {
        material.roughness = preset.roughness;
      }
      if (material.metalness !== undefined) {
        material.metalness = preset.metalness;
      }
      if (material.clearcoat !== undefined) {
        material.clearcoat = preset.clearcoat;
      }
      if (material.clearcoatRoughness !== undefined) {
        material.clearcoatRoughness = preset.clearcoatRoughness;
      }
      if (material.transparent !== undefined) {
        material.transparent = preset.transparent;
      }
      if (material.opacity !== undefined) {
        material.opacity = preset.opacity;
      }
      if (material.envMapIntensity !== undefined) {
        material.envMapIntensity = preset.envMapIntensity || 1.0;
      }

      // Mark material for update
      material.needsUpdate = true;
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history for preset application
    if (addToHistory) {
      addToHistoryImmediate();
    }
  };

  const updateMaterialProperty = (property, value, isSlider = false) => {
    // Update local state
    setMaterialProps((prev) => ({
      ...prev,
      [property]: value,
    }));

    // Update all selected objects
    selectedObjects.forEach((obj) => {
      if (obj && obj.material) {
        if (property === "color") {
          obj.material[property] = value;
        } else {
          obj.material[property] = value;
        }
        obj.material.needsUpdate = true;
      }
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history with appropriate debouncing
    if (addToHistory) {
      if (isSlider) {
        addToHistoryDebounced();
      } else {
        addToHistoryImmediate();
      }
    }
  };

  const changeMaterialType = (type) => {
    // Update local state
    setMaterialType(type);

    // Update all selected objects
    selectedObjects.forEach((obj) => {
      if (!obj) return;

      const currentColor =
        obj.material && obj.material.color
          ? obj.material.color.getHex()
          : 0xffffff;

      let newMaterial;

      switch (type) {
        case "basic":
          newMaterial = new THREE.MeshBasicMaterial({ color: currentColor });
          break;
        case "standard":
          newMaterial = new THREE.MeshStandardMaterial({
            color: currentColor,
            roughness: materialProps.roughness,
            metalness: materialProps.metalness,
          });
          break;
        case "physical":
          newMaterial = new THREE.MeshPhysicalMaterial({
            color: currentColor,
            roughness: materialProps.roughness,
            metalness: materialProps.metalness,
            clearcoat: materialProps.clearcoat,
            clearcoatRoughness: materialProps.clearcoatRoughness,
          });
          break;
        case "toon":
          newMaterial = new THREE.MeshToonMaterial({ color: currentColor });
          break;
        case "normal":
          newMaterial = new THREE.MeshNormalMaterial();
          break;
        default:
          newMaterial = new THREE.MeshStandardMaterial({ color: currentColor });
      }

      // Copy other properties
      newMaterial.wireframe = materialProps.wireframe;
      newMaterial.transparent = materialProps.transparent;
      newMaterial.opacity = materialProps.opacity;

      obj.material = newMaterial;
    });
    if (forceUpdate) forceUpdate();
    if (setMaterialVersion) setMaterialVersion((v) => v + 1);

    // Add to history for material type changes
    if (addToHistory) {
      addToHistoryImmediate();
    }
  };

  return (
    <div className="w-full overflow-y-auto text-xs">
      <div className="mb-4">
        <div className="mb-2 border-b border-gray-700 text-[#FDE9CE]">
          Material{" "}
          {selectedObjects.length > 1
            ? `(${selectedObjects.length} objects)`
            : selectedObjects.length === 1
            ? `(${selectedObjects[0].name || "Unnamed object"})`
            : ""}
        </div>

        {/* Material Presets Section */}
        <div className="mb-4 p-4 bg-gradient-to-br from-[#2A2D3A] to-[#1A1D2A] rounded-xl border border-[#A3A3A3]/10 shadow-lg">
          <h3 className="text-[#FDE9CE] font-medium mb-4 text-center text-sm tracking-wide">
            Material Presets
          </h3>

          {/* Gems Section */}
          <div className="mb-5">
            <h4 className="text-[#A3A3A3] text-[10px] uppercase tracking-wider mb-3 font-semibold flex items-center">
              <span className="w-2 h-2 rounded-full bg-emerald-500 mr-2"></span>
              Gems
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {MATERIAL_PRESETS.gems.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  className="group relative overflow-hidden rounded-xl border border-[#A3A3A3]/20 hover:border-[#FDE9CE]/50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#FDE9CE]/5"
                >
                  <div
                    className="h-10 w-full relative"
                    style={{
                      background: `linear-gradient(135deg, ${preset.color} 0%, ${preset.color}dd 50%, ${preset.color}bb 100%)`,
                      boxShadow: `inset 0 1px 2px rgba(255,255,255,0.3), inset 0 -1px 2px rgba(0,0,0,0.3)`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-60"></div>
                    {preset.transparent && (
                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>
                    )}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-[10px] font-bold text-white bg-black/30 px-2 py-1 rounded-full">
                        Apply
                      </span>
                    </div>
                  </div>
                  <div className="p-1.5 bg-gradient-to-b from-[#2A2D3A] to-[#1F222F]">
                    <span className="text-[10px] text-[#FDE9CE] group-hover:text-white transition-colors duration-200 font-medium">
                      {preset.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Metals Section */}
          <div>
            <h4 className="text-[#A3A3A3] text-[10px] uppercase tracking-wider mb-3 font-semibold flex items-center">
              <span className="w-2 h-2 rounded-full bg-amber-500 mr-2"></span>
              Metals
            </h4>
            <div className="grid grid-cols-3 gap-3">
              {MATERIAL_PRESETS.metals.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => applyPreset(preset)}
                  className="group relative overflow-hidden rounded-xl border border-[#A3A3A3]/20 hover:border-[#FDE9CE]/50 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-[#FDE9CE]/5"
                >
                  <div
                    className="h-10 w-full relative"
                    style={{
                      background: `linear-gradient(135deg, ${preset.color} 0%, ${preset.color}cc 50%, ${preset.color}aa 100%)`,
                      boxShadow: `inset 0 1px 2px rgba(255,255,255,0.4), inset 0 -1px 2px rgba(0,0,0,0.4)`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent opacity-50"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-30"></div>
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <span className="text-[10px] font-bold text-white bg-black/30 px-2 py-1 rounded-full">
                        Apply
                      </span>
                    </div>
                  </div>
                  <div className="p-1.5 bg-gradient-to-b from-[#2A2D3A] to-[#1F222F]">
                    <span className="text-[10px] text-[#FDE9CE] group-hover:text-white transition-colors duration-200 font-medium">
                      {preset.name}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="mb-3">
          <label className="text-[#FDE9CE]">Material Type</label>
          <select
            value={materialType}
            onChange={(e) => changeMaterialType(e.target.value)}
            className="ml-2 bg-[#1A1A1A] border border-gray-700 rounded px-2 py-1 w-full mt-1 text-[#FDE9CE]"
          >
            <option value="standard">Standard</option>
            <option value="physical">Physical</option>
            <option value="basic">Basic</option>
            <option value="toon">Toon</option>
            <option value="normal">Normal</option>
          </select>
        </div>

        <div className="mb-3">
          <label className="text-[#FDE9CE]">Color</label>
          <div className="flex items-center gap-2 mt-1">
            <input
              type="color"
              value={materialProps.color || "#ffffff"}
              onChange={(e) => {
                const color = e.target.value;
                setMaterialProps((prev) => ({ ...prev, color }));
                selectedObjects.forEach((obj) => {
                  if (obj && obj.material) {
                    obj.material.color = new THREE.Color(color);
                    obj.material.needsUpdate = true;
                  }
                });
              }}
              className="w-12 h-12 rounded cursor-pointer p-0 border border-gray-700"
            />
            <input
              type="text"
              value={materialProps.color || "#ffffff"}
              onChange={(e) => {
                const value = e.target.value;
                setMaterialProps((prev) => ({ ...prev, color: value }));
                if (/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  selectedObjects.forEach((obj) => {
                    if (obj && obj.material) {
                      obj.material.color = new THREE.Color(value);
                      obj.material.needsUpdate = true;
                    }
                  });
                }
              }}
              onBlur={(e) => {
                const value = e.target.value;
                if (!/^#[0-9A-Fa-f]{6}$/.test(value)) {
                  setMaterialProps((prev) => ({
                    ...prev,
                    color: materialProps.color || "#ffffff",
                  }));
                }
              }}
              className="bg-[#1A1A1A] border border-gray-700 rounded px-2 py-1 w-24 text-[#FDE9CE]"
              placeholder="#FFFFFF"
            />
          </div>
        </div>

        {(materialType === "standard" || materialType === "physical") && (
          <>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">Roughness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.roughness || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "roughness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.roughness || 0.5).toFixed(2)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Metalness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.metalness || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "metalness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.metalness || 0.5).toFixed(2)}
                </span>
              </div>
            </div>
          </>
        )}

        {materialType === "physical" && (
          <>
            <div className="mb-3">
              <label className="text-[#FDE9CE]">Clearcoat</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.clearcoat || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "clearcoat",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.clearcoat || 0.5).toFixed(2)}
                </span>
              </div>
            </div>

            <div className="mb-3">
              <label className="text-[#FDE9CE]">Clearcoat Roughness</label>
              <div className="flex items-center gap-2 mt-1">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={materialProps.clearcoatRoughness || 0.5}
                  onChange={(e) =>
                    updateMaterialProperty(
                      "clearcoatRoughness",
                      parseFloat(e.target.value),
                      true
                    )
                  }
                  className="w-full accent-[#FDE9CE]"
                />
                <span className="w-10 text-right text-[#FDE9CE]">
                  {(materialProps.clearcoatRoughness || 0.5).toFixed(2)}
                </span>
              </div>
            </div>
          </>
        )}

        <div className="mb-3">
          <label className="flex items-center gap-2 text-[#FDE9CE]">
            <input
              type="checkbox"
              checked={materialProps.wireframe || false}
              onChange={(e) =>
                updateMaterialProperty("wireframe", e.target.checked)
              }
              className="accent-[#FDE9CE]"
            />
            Wireframe
          </label>
        </div>

        <div className="mb-3">
          <label className="flex items-center gap-2 text-[#FDE9CE]">
            <input
              type="checkbox"
              checked={materialProps.transparent || false}
              onChange={(e) =>
                updateMaterialProperty("transparent", e.target.checked)
              }
              className="accent-[#FDE9CE]"
            />
            Transparent
          </label>
        </div>

        {materialProps.transparent && (
          <div className="mb-3">
            <label className="text-[#FDE9CE]">Opacity</label>
            <div className="flex items-center gap-2 mt-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={materialProps.opacity || 1.0}
                onChange={(e) =>
                  updateMaterialProperty(
                    "opacity",
                    parseFloat(e.target.value),
                    true
                  )
                }
                className="w-full accent-[#FDE9CE]"
              />
              <span className="w-10 text-right text-[#FDE9CE]">
                {(materialProps.opacity || 1.0).toFixed(2)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
