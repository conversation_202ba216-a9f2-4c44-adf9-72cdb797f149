/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 public/models/diamond-ring.glb 
*/

import React, { useEffect, useRef } from "react";
import { useGLTF } from "@react-three/drei";

export function DiamondRing(props) {
  const { onLoad, onClick, ...restProps } = props;
  const group = useRef();
  const { nodes, materials } = useGLTF("/models/diamond-ring.glb");

  // Add this useEffect to register objects with the scene
  useEffect(() => {
    if (onLoad) {
      // Pass the group ref to the onLoad callback
      onLoad(group.current);
    }

    // Make objects respond to clicks
    if (group.current) {
      group.current.traverse((child) => {
        if (child.isMesh) {
          child.onClick = onClick;
        }
      });
    }
  }, [onLoad, onClick]);

  return (
    <group ref={group} {...restProps} dispose={null} scale={0.1}>
      <mesh
        name="Diamond"
        geometry={nodes.Diamond.geometry}
        material={materials.Diamond}
        position={[-0.001, 64.836, 0.047]}
        scale={2834.418}
        onClick={onClick}
      />
      <mesh
        name="White_Gold_Band"
        geometry={nodes.White.geometry}
        material={materials.White}
        position={[0.001, 50.65, 0.016]}
        scale={100}
        onClick={onClick}
      />
    </group>
  );
}

useGLTF.preload("/models/diamond-ring.glb");
