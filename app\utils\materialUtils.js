import * as THREE from "three";

export const createMaterialFromConfig = (savedMaterial, currentColor) => {
  let newMaterial;

  switch (savedMaterial.type) {
    case "MeshBasicMaterial":
      newMaterial = new THREE.MeshBasicMaterial({
        color: currentColor,
        wireframe: savedMaterial.wireframe,
        transparent: savedMaterial.transparent,
        opacity: savedMaterial.opacity,
      });
      break;
    case "MeshStandardMaterial":
      newMaterial = new THREE.MeshStandardMaterial({
        color: currentColor,
        roughness: savedMaterial.roughness,
        metalness: savedMaterial.metalness,
        wireframe: savedMaterial.wireframe,
        transparent: savedMaterial.transparent,
        opacity: savedMaterial.opacity,
        envMapIntensity: savedMaterial.envMapIntensity,
      });
      break;
    case "MeshPhysicalMaterial":
      newMaterial = new THREE.MeshPhysicalMaterial({
        color: currentColor,
        roughness: savedMaterial.roughness,
        metalness: savedMaterial.metalness,
        clearcoat: savedMaterial.clearcoat,
        clearcoatRoughness: savedMaterial.clearcoatRoughness,
        wireframe: savedMaterial.wireframe,
        transparent: savedMaterial.transparent,
        opacity: savedMaterial.opacity,
        envMapIntensity: savedMaterial.envMapIntensity,
      });
      break;
    case "MeshToonMaterial":
      newMaterial = new THREE.MeshToonMaterial({
        color: currentColor,
        wireframe: savedMaterial.wireframe,
        transparent: savedMaterial.transparent,
        opacity: savedMaterial.opacity,
      });
      break;
    case "MeshNormalMaterial":
      newMaterial = new THREE.MeshNormalMaterial({
        wireframe: savedMaterial.wireframe,
      });
      break;
    default:
      newMaterial = new THREE.MeshStandardMaterial({
        color: currentColor,
        roughness: savedMaterial.roughness,
        metalness: savedMaterial.metalness,
        wireframe: savedMaterial.wireframe,
        transparent: savedMaterial.transparent,
        opacity: savedMaterial.opacity,
        envMapIntensity: savedMaterial.envMapIntensity,
      });
  }

  return newMaterial;
};

export const applyMaterialToObject = (obj, savedMaterial) => {
  if (!obj.material || !savedMaterial) return;

  const currentColor = obj.material.color
    ? obj.material.color.getHex()
    : 0xffffff;

  const newMaterial = createMaterialFromConfig(savedMaterial, currentColor);

  if (newMaterial) {
    if (savedMaterial.color && newMaterial.color) {
      try {
        newMaterial.color.set("#" + savedMaterial.color);
      } catch (error) {
        console.warn(`Failed to set color for material ${obj.name}:`, error);
      }
    }

    obj.material = newMaterial;
    obj.material.needsUpdate = true;
  }
};
