/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 public/models/Computer.glb --transform 
Files: public/models/Computer.glb [222.27MB] > C:\Users\<USER>\OneDrive\Documents\Work\agape-optimized-environment\Computer-transformed.glb [3.71MB] (98%)
*/

import React, { useEffect } from "react";
import { useGLTF, useAnimations } from "@react-three/drei";

export function Computer(props) {
  const { onLoad, onClick, ...restProps } = props;
  const group = React.useRef();
  const { nodes, materials, animations } = useGLTF(
    "/models/Computer_Compressed.glb"
  );
  const { actions } = useAnimations(animations, group);

  useEffect(() => {
    if (onLoad) {
      onLoad(group.current);
    }

    group.current.traverse((child) => {
      if (child.isMesh) {
        child.onClick = onClick;
      }
    });
  }, [onLoad, onClick]);

  return (
    <group ref={group} {...restProps} dispose={null}>
      <group name="Scene">
        <mesh
          name="FrontPanel_low"
          castShadow
          receiveShadow
          geometry={nodes.FrontPanel_low.geometry}
          material={materials["case"]}
          position={[0.001, 0.235, -0.023]}
          scale={0.262}
          onClick={onClick}
        />
        <group name="SideRightTop_low" position={[0.047, 0.385, 0.046]}>
          <mesh
            name="Cube280"
            castShadow
            receiveShadow
            geometry={nodes.Cube280.geometry}
            material={materials["case"]}
            position={[0.03, -0.095, -0.019]}
            scale={0.076}
            onClick={onClick}
          />
        </group>
        <group name="SideRightTop_low001" position={[0.047, 0.219, 0.046]}>
          <mesh
            name="Cube304"
            castShadow
            receiveShadow
            geometry={nodes.Cube304.geometry}
            material={materials["case"]}
            position={[0.03, -0.062, -0.019]}
            scale={0.043}
            onClick={onClick}
          />
        </group>
        <mesh
          name="topCoverSmall_low001"
          castShadow
          receiveShadow
          geometry={nodes.topCoverSmall_low001.geometry}
          material={materials.FanLP_Emission}
          position={[0, 0.245, 0]}
          scale={0.239}
          onClick={onClick}
        />
        <mesh
          name="MainMB_low"
          castShadow
          receiveShadow
          geometry={nodes.MainMB_low.geometry}
          material={materials.MB_low}
          position={[0.018, 0.224, -0.093]}
          scale={0.153}
          onClick={onClick}
        />
        <mesh
          name="ram"
          castShadow
          receiveShadow
          geometry={nodes.ram.geometry}
          material={materials.ram}
          position={[0.018, 0.278, 0.001]}
          scale={0.07}
          onClick={onClick}
        />
        <mesh
          name="GC_Low"
          castShadow
          receiveShadow
          geometry={nodes.GC_Low.geometry}
          material={materials.GC_LP}
          position={[-0.022, 0.148, -0.066]}
          scale={0.136}
          onClick={onClick}
        />
        <mesh
          name="CoolerScreen_low001"
          castShadow
          receiveShadow
          geometry={nodes.CoolerScreen_low001.geometry}
          material={materials.CoolerLP_Material}
          position={[-0.014, 0.357, 0.005]}
          scale={0.202}
          onClick={onClick}
        />
        <mesh
          name="Power_LP002"
          castShadow
          receiveShadow
          geometry={nodes.Power_LP002.geometry}
          material={materials.powerPlug}
          position={[0.101, 0.13, -0.169]}
          scale={0.094}
          onClick={onClick}
        />
        <mesh
          name="Plane030"
          castShadow
          receiveShadow
          geometry={nodes.Plane030.geometry}
          material={materials.FanLP}
          position={[-0.005, 0.234, 0.001]}
          rotation={[Math.PI / 2, -1.571, 0]}
          scale={0.232}
          onClick={onClick}
        />
        <mesh
          name="RearPlate_low007"
          castShadow
          receiveShadow
          geometry={nodes.RearPlate_low007.geometry}
          material={nodes.RearPlate_low007.material}
          position={[0.044, 0.132, -0.233]}
          rotation={[0, 0, -Math.PI / 2]}
          scale={0.111}
          onClick={onClick}
        />
      </group>
    </group>
  );
}

// useGLTF.preload("/models/Computer_Compressed.glb");
