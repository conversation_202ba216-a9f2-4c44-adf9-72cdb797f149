export const calculateModelStats = (scene) => {
  let vertexCount = 0;
  let triangleCount = 0;
  const materials = new Set();

  scene.traverse((object) => {
    if (object.isMesh) {
      const geometry = object.geometry;
      vertexCount += geometry.attributes.position.count;
      triangleCount += geometry.index
        ? geometry.index.count / 3
        : geometry.attributes.position.count / 3;
      materials.add(object.material);
    }
  });

  return {
    vertices: vertexCount,
    triangles: triangleCount,
    materials: materials.size,
    animations: scene.animations?.length || 0,
  };
};

export const setShadowsOnModel = (scene) => {
  scene.traverse((child) => {
    if (child.isMesh) {
      child.castShadow = true;
      child.receiveShadow = true;
    }
  });
};

export const getDefaultDecalPosition = (modelId) => {
  const modelConfigs = {
    chosenring: [0, 1.68, 0],
    testring: [0, 1.68, 0],
    agapering1: [0, 1.2, 0],
    agapering2: [0, 1.2, 0],
    ringa: [0, 0.26, 0],
    ringb: [0, 0.5, 0],
    ringc: [0, 0.26, 0],
  };
  return modelConfigs[modelId] || [0, 1.68, 0];
};

export const getDefaultDecalRotation = (modelId) => {
  const modelConfigs = {
    chosenring: [-Math.PI / 2, 0, 0],
    testring: [-Math.PI / 2, 0, 0],
    agapering1: [-Math.PI / 2, 0, 0],
    agapering2: [-Math.PI / 2, 0, 0],
    ringa: [-Math.PI / 2, 0, 0],
    ringb: [-Math.PI / 2, 0, 0],
    ringc: [-Math.PI / 2, 0, 0],
  };
  return modelConfigs[modelId] || [-Math.PI / 2, 0, 0];
};

export const getDefaultDecalScale = (modelId) => {
  const modelConfigs = {
    chosenring: [6.0, 2.0, 1.5],
    testring: [6.0, 2.0, 1.5],
    agapering1: [3.2, 1.8, 1.0],
    agapering2: [3.2, 1.8, 1.0],
    ringa: [1.8, 2.0, 0.8],
    ringb: [1.8, 2.0, 0.8],
    ringc: [1.8, 2.0, 0.8],
  };
  return modelConfigs[modelId] || [4.2, 2.0, 1.5];
};

export const calculateDynamicScale = (baseScale, text, modelId) => {
  if (!text || text.length === 0) return baseScale;

  const textLength = text.length;
  let [scaleX, scaleY, scaleZ] = baseScale;

  // Calculate scale multiplier based on text length
  let scaleMultiplier = 1.0;

  if (textLength > 10) {
    scaleMultiplier = 1.3 + (textLength - 10) * 0.08;
  } else if (textLength > 6) {
    scaleMultiplier = 1.0 + (textLength - 6) * 0.08;
  } else if (textLength < 4) {
    scaleMultiplier = 0.8 + textLength * 0.05;
  }

  scaleX = scaleX * scaleMultiplier;

  if (textLength > 8) {
    scaleY = scaleY * Math.min(1.2, 1.0 + (textLength - 8) * 0.02);
  }

  const maxScale = {
    chosenring: [12.0, 2.5, 2.0],
    testring: [12.0, 2.5, 2.0],
    agapering1: [6.5, 2.2, 1.5],
    agapering2: [6.5, 2.2, 1.5],
    ringa: [4.0, 2.5, 1.2],
    ringb: [4.0, 2.5, 1.2],
    ringc: [4.0, 2.5, 1.2],
  };

  const limits = maxScale[modelId] || [8.5, 2.5, 2.0];
  scaleX = Math.min(scaleX, limits[0]);
  scaleY = Math.min(scaleY, limits[1]);
  scaleZ = Math.min(scaleZ, limits[2]);

  return [scaleX, scaleY, scaleZ];
};
