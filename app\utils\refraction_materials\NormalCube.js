import {
  CubeCamera,
  DoubleSide,
  Mesh,
  Scene,
  ShaderMaterial,
  WebGLCubeRenderTarget,
  LinearFilter,
  NoColorSpace,
  NearestFilter,
} from "three";

class NormalSDFMaterial extends ShaderMaterial {
  constructor() {
    //
    super({
      side: DoubleSide,
    });
    this.side = DoubleSide;

    this.uniforms = {
      objectBoundingSphereRadius: { value: 1 },
    };

    this.vertexShader = `
    varying vec3 v_Normal;
    varying vec3 v_vertexPosition;


    void main() {
        v_vertexPosition = vec3(modelMatrix * vec4(position, 1.0));
        v_Normal = normal;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position.xyz, 1.0 );
    }
        `;

    this.fragmentShader = `
        varying vec3 v_Normal;
        varying vec3 v_vertexPosition;
        uniform float objectBoundingSphereRadius;
        void main (void) {
            vec3 normalizedColor = normalize(v_Normal);
            vec3 encodeData = normalizedColor * 0.5 + 0.5;

            gl_FragColor = vec4( encodeData.x, encodeData.y, encodeData.z, 
                length(v_vertexPosition) / objectBoundingSphereRadius 
            );
        }
        
    `;
  }
}

export class NormalCube {
  constructor({ renderer, geo }) {
    let geo2 = geo.clone();
    geo2.center();
    geo2.computeBoundingSphere();
    geo2.computeVertexNormals();

    let normalmapMesh = new Mesh(geo2, new NormalSDFMaterial({}));

    let boundingRadius = geo2.boundingSphere.radius;

    let resolutionX = 128;
    let resolutionY = 128;

    if (boundingRadius >= 1) {
      resolutionX = 256;
      resolutionY = 256;
    }

    if (boundingRadius >= 2) {
      resolutionX = 512;
      resolutionY = 512;
    }

    if (boundingRadius >= 3) {
      resolutionX = 512;
      resolutionY = 512;
    }

    //console.log('boundingRadius', boundingRadius, 'res', resolutionX)

    let rtt = new WebGLCubeRenderTarget(resolutionX, resolutionY, {
      stencilBuffer: false,
      depthBuffer: false,
      // colorSpace: LinearSRGBColorSpace,
      colorSpace: NoColorSpace,
      generateMipmaps: true,
      minFilter: LinearFilter,
      magFilter: NearestFilter,
      samples: 8,
      anisotropy: 1,
    });

    rtt.texture.magFilter = rtt.texture.minFilter = NearestFilter;

    let cubeNormalCamera = new CubeCamera(0.1, 500, rtt);

    let scene = new Scene();

    scene.add(cubeNormalCamera);
    scene.add(normalmapMesh);

    //console.log('normalmapMesh', renderer);

    normalmapMesh.material.uniforms.objectBoundingSphereRadius.value =
      geo2.boundingSphere.radius * 1.0;

    cubeNormalCamera.update(renderer, scene);

    rtt.needsUpdate = true;

    this.updateWhenNeeded = () => {
      if (this.needsUpdate) {
        cubeNormalCamera.update(renderer, scene);
      }
    };

    this.clean = () => {
      geo2.dispose();
      rtt.dispose();
    };

    this.scene = scene;
    this.cubeNormalCamera = cubeNormalCamera;
    this.texture = rtt.texture;
    this.needsUpdate = true;
  }
}
