"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

export function SceneSwitcher({ selectedModel, onSelectModel }) {
  const models = [
    { id: "chosenring", name: "Chosen Ring" },
    { id: "testring", name: "Chosen Ring Variant" },
    { id: "agapering1", name: "Agape Ring 1" },
    { id: "agapering2", name: "Agape Ring 2" },
    // { id: "jewelry01", name: "Jewelry 01" },
    // { id: "jewelry02", name: "Agape Ring" },
    // { id: "jewelry03", name: "Silver Ring" },
    // { id: "jewelry05", name: "Pendant Brooch" },
    { id: "ringa", name: "Ring A" },
    { id: "ringb", name: "Ring B" },
    { id: "ringc", name: "Ring C" },
    { id: "jewelry04", name: "Heart Pendant" },
    // { id: "mech", name: "Mech" },
  ];

  // Find the display index based on the currently selected model
  const getDisplayIndex = () => {
    const index = models.findIndex((model) => model.id === selectedModel);
    return index !== -1 ? index : 0;
  };

  // Handle next model button click
  const handleNextModel = () => {
    const currentIndex = getDisplayIndex();
    const nextIndex = (currentIndex + 1) % models.length;
    onSelectModel(models[nextIndex].id);
  };

  // Handle previous model button click
  const handlePrevModel = () => {
    const currentIndex = getDisplayIndex();
    const prevIndex = (currentIndex - 1 + models.length) % models.length;
    onSelectModel(models[prevIndex].id);
  };

  return (
    <div className="flex items-center gap-1">
      <button
        onClick={handlePrevModel}
        className="p-1.5 text-white/70 hover:text-white transition-colors duration-200"
        title="Previous Model"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15 18L9 12L15 6"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>

      <button
        onClick={handleNextModel}
        className="flex items-center justify-center gap-2 px-4 py-1.5 text-white rounded-lg bg-gradient-to-tl from-[#32343D] to-[#0F132A] hover:bg-gradient-to-tl hover:from-[#32343D] hover:to-[#14192D] transition-all duration-200 min-w-[160px]"
        title="Next Model"
      >
        <span className="text-xs font-medium">
          {models[getDisplayIndex()].name}
        </span>
        <Image
          src="/images/Group 325.svg"
          alt="Model switcher icon"
          width={16}
          height={16}
        />
      </button>

      <button
        onClick={handleNextModel}
        className="p-1.5 text-white/70 hover:text-white transition-colors duration-200"
        title="Next Model"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M9 6L15 12L9 18"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>
    </div>
  );
}
