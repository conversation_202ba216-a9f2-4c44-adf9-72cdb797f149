import * as THREE from "three";

// Helper function for linear interpolation
export function lerp(start, end, t) {
  return start * (1 - t) + end * t;
}

// Helper function to lerp between two Vector3 objects
export function lerpVector(v1, v2, t) {
  return new THREE.Vector3(
    lerp(v1.x, v2.x, t),
    lerp(v1.y, v2.y, t),
    lerp(v1.z, v2.z, t)
  );
}

// Helper function for smooth easing
export function easeInOutCubic(t) {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
}

export const predefinedAnimations = [
  {
    id: "float",
    name: "Float",
    description: "Gentle floating animation for the model",
    duration: 2000,
    loop: true,
    animate: () => {
      // Don't modify camera or controls - let the Float component handle the animation
      return;
    },
  },
  {
    id: "showcase",
    name: "Showcase",
    description: "Smooth orbit around the model",
    duration: 8000, // milliseconds
    loop: true,
    animate: (
      camera,
      controls,
      progress,
      target = new THREE.Vector3(0, 0, 0)
    ) => {
      // Calculate the current angle based on progress (0-1)
      const angle = progress * Math.PI * 2;

      // Create a circular path with adjusted height
      const radius = 3.5; // Even closer
      const height = 3.5 + Math.sin(progress * Math.PI * 2) * 0.2; // Much higher and minimal vertical movement

      // Set camera position
      const x = Math.cos(angle) * radius;
      const z = Math.sin(angle) * radius;
      camera.position.set(x, height, z);

      // Keep looking at the center but slightly above
      const adjustedTarget = target.clone().add(new THREE.Vector3(0, 1, 0));
      controls.target.copy(adjustedTarget);
      controls.update();
    },
  },
  {
    id: "inspection",
    name: "Inspection",
    description: "Views from all angles",
    duration: 10000,
    loop: true,
    animate: (
      camera,
      controls,
      progress,
      target = new THREE.Vector3(0, 0, 0)
    ) => {
      // Define key viewpoints to cycle through - much higher angles
      const viewpoints = [
        {
          pos: new THREE.Vector3(2.5, 3.5, 2.5),
          target: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        }, // front-right, high angle
        {
          pos: new THREE.Vector3(2.5, 3, 0),
          target: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        }, // right, high angle
        {
          pos: new THREE.Vector3(0, 3.5, 2.5),
          target: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        }, // front, high angle
        {
          pos: new THREE.Vector3(-2.5, 3.5, -2.5),
          target: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        }, // back-left, high angle
        {
          pos: new THREE.Vector3(0, 4, 0),
          target: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        }, // top view
      ];

      // Number of segments (transitions between viewpoints)
      const segments = viewpoints.length;
      const segmentProgress = (progress * segments) % segments;
      const currentSegment = Math.floor(segmentProgress);
      const segmentT = segmentProgress - currentSegment;

      // Get current and next viewpoints
      const currentView = viewpoints[currentSegment];
      const nextView = viewpoints[(currentSegment + 1) % viewpoints.length];

      // Apply easing for smoother transitions
      const t = easeInOutCubic(segmentT);

      // Interpolate camera position
      camera.position.copy(lerpVector(currentView.pos, nextView.pos, t));

      // Update controls
      controls.target.copy(target);
      controls.update();
    },
  },
  {
    id: "details",
    name: "Zoom Details",
    description: "Focuses on different model details",
    duration: 15000,
    loop: true,
    animate: (
      camera,
      controls,
      progress,
      target = new THREE.Vector3(0, 0, 0)
    ) => {
      // Define viewpoints to focus on - much higher angles
      const viewpoints = [
        // Front close-up
        {
          pos: new THREE.Vector3(0, 2.5, 2),
          focus: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        },
        // Right close-up
        {
          pos: new THREE.Vector3(2, 2.2, 0),
          focus: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        },
        // Back close-up
        {
          pos: new THREE.Vector3(0, 2.4, -2),
          focus: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        },
        // Left close-up
        {
          pos: new THREE.Vector3(-2, 2.2, 0),
          focus: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        },
        // Top-angled close-up
        {
          pos: new THREE.Vector3(1.5, 3.5, 1.5),
          focus: target.clone().add(new THREE.Vector3(0, 1.5, 0)),
        },
      ];

      // Calculate segment and progress
      const segments = viewpoints.length;
      const segmentProgress = (progress * segments) % segments;
      const currentSegment = Math.floor(segmentProgress);
      const nextSegment = (currentSegment + 1) % segments;

      // Get current and next viewpoints
      const currentView = viewpoints[currentSegment];
      const nextView = viewpoints[nextSegment];

      // Use a modified easing function for extra smoothness
      // 40% time at position, 60% time transitioning - for a more fluid motion
      let positionT, focusT;

      if (segmentProgress - currentSegment < 0.4) {
        // During the hold phase, we stay at the current position without any bouncing
        positionT = 0;
        focusT = 0;
      } else {
        // During the transition phase, smoothly move to the next position
        // Map the progress from 0.4-1.0 range to 0.0-1.0 range
        const transitionProgress =
          (segmentProgress - currentSegment - 0.4) / 0.6;

        // Apply double cubic easing for ultra-smooth transitions
        positionT = easeInOutCubic(easeInOutCubic(transitionProgress));
        focusT = easeInOutCubic(transitionProgress);
      }

      // Calculate interpolated position and focus
      const position = lerpVector(currentView.pos, nextView.pos, positionT);
      const focusPoint = lerpVector(currentView.focus, nextView.focus, focusT);

      // Apply position and focus
      camera.position.copy(position);
      controls.target.copy(focusPoint);
      controls.update();
    },
  },
];
