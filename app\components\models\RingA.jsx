import React, { useRef, useMemo } from "react";
import {
  useGLTF,
  Decal,
  RenderTexture,
  Text,
  PerspectiveCamera,
} from "@react-three/drei";
import { EngravingMaterial } from "../../utils/TextBumpMapGenerator.jsx";

export function RingA(props) {
  const groupRef = useRef();
  const { nodes, materials } = useGLTF("/models/Ring_TypeA.glb");

  // Log when clicked to help debug
  const handleClick = (e) => {
    console.log("RingA clicked:", e.object);
    e.stopPropagation();

    if (props.isPlacingDecal && props.onDecalPlacement) {
      props.onDecalPlacement(e);
      return; // Stop further processing if placing decal
    }

    // Ensure the event has the correct object property
    if (props.onClick) {
      // If clicking a child mesh, ensure the object ref is passed correctly
      if (e.object !== groupRef.current) {
        props.onClick(e);
      } else {
        // If clicking the group, create a similar event with the group as target
        const newEvent = { ...e, object: groupRef.current };
        props.onClick(newEvent);
      }
    }
  };

  // Create a unique key for the decal that changes when font or color changes
  const decalKey = useMemo(() => {
    return `${props.font}-${props.decalColor}-${props.engravingText}`;
  }, [props.font, props.decalColor, props.engravingText]);

  // Calculate dynamic font size based on text length (smaller base size for RingA)
  const calculateFontSize = useMemo(() => {
    if (!props.engravingText) return 2;

    const textLength = props.engravingText.length;
    let baseFontSize = 2;

    // Adjust font size based on text length
    if (textLength > 12) {
      // For very long text, reduce font size more aggressively
      baseFontSize = Math.max(1, 2 - (textLength - 12) * 0.1);
    } else if (textLength > 8) {
      // For medium-long text, reduce font size moderately
      baseFontSize = Math.max(1.3, 2 - (textLength - 8) * 0.15);
    } else if (textLength > 6) {
      // For slightly long text, reduce font size slightly
      baseFontSize = Math.max(1.6, 2 - (textLength - 6) * 0.1);
    } else if (textLength < 3) {
      // For very short text, slightly increase font size
      baseFontSize = Math.min(2.8, 2 + (3 - textLength) * 0.15);
    }

    return baseFontSize;
  }, [props.engravingText]);

  return (
    <group
      ref={groupRef}
      {...props}
      dispose={null}
      onClick={handleClick}
      name="RingA"
      userData={{ selectable: true }}
    >
      <mesh
        castShadow
        receiveShadow
        name="Ring_TypeA_Band_01"
        geometry={nodes.Ring_TypeA_Band_01.geometry}
        material={materials.Silver}
        position={[0.007, 0.26, 0]}
        rotation={[0, 0, -Math.PI / 2]}
        scale={1.088}
        onClick={handleClick}
        userData={{ selectable: true, part: "band" }}
      >
        {props.engravingText && (
          <Decal
            key={decalKey}
            position={props.decalPosition || [0, 0, 0.9]}
            rotation={props.decalRotation || [Math.PI / 2, 0, 0]}
            scale={props.decalScale || [1.8, 2.0, 0.8]}
            debug={props.showDecalDebug || false}
          >
            <EngravingMaterial
              text={props.engravingText}
              font={props.font}
              fontSize={calculateFontSize}
              color="#2c2c2c"
              decalColor={props.decalColor || "#000000"}
              aspect={3}
            />
          </Decal>
        )}
        <mesh
          name="Ring_TypeA_Band_02"
          castShadow
          receiveShadow
          geometry={nodes.Ring_TypeA_Band_02.geometry}
          material={materials.Metal_Black}
          onClick={handleClick}
          userData={{ selectable: true, part: "accent" }}
        />
        <mesh
          name="Ring_TypeA_Band_03"
          castShadow
          receiveShadow
          geometry={nodes.Ring_TypeA_Band_03.geometry}
          material={materials.Metal_Black}
          position={[0.05, 0.002, 0]}
          scale={[1, 0.567, 1]}
          onClick={handleClick}
          userData={{ selectable: true, part: "accent" }}
        />
        <mesh
          name="Ring_TypeA_Crown"
          castShadow
          receiveShadow
          geometry={nodes.Ring_TypeA_Crown.geometry}
          material={materials.Silver}
          position={[-1.054, 0.029, 0.001]}
          scale={3.67}
          onClick={handleClick}
          userData={{ selectable: true, part: "crown" }}
        />
        <mesh
          name="Ring_TypeA_Gem"
          castShadow
          receiveShadow
          geometry={nodes.Ring_TypeA_Gem.geometry}
          material={materials.Gem_Black}
          position={[-1.109, 0.006, 0.001]}
          scale={[1.109, 0.78, 0.916]}
          onClick={handleClick}
          userData={{ selectable: true, part: "gem" }}
        />
      </mesh>
    </group>
  );
}

// useGLTF.preload("/models/Ring_TypeA.glb");
