"use client";

import Image from "next/image";
import { useState, useEffect } from "react";
import { ChevronDown, Play, Pause, ChevronRight } from "lucide-react";
import { predefinedAnimations } from "./animations/CameraAnimations";

// Add useMediaQuery hook
const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    const listener = () => setMatches(media.matches);
    media.addEventListener("change", listener);
    return () => media.removeEventListener("change", listener);
  }, [matches, query]);

  return matches;
};

export function Outliner({
  sceneObjects,
  selectedObjects,
  onSelectObject,
  onCameraView,
  onPlayAnimation,
  groundType,
  setGroundType,
  collapsed,
  setCollapsed,
}) {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [expandedItems, setExpandedItems] = useState({
    objects: true,
    cameras: false,
    animations: false,
    ground: true,
  });
  const [internalCollapsed, setInternalCollapsed] = useState(() => {
    // Initialize based on device type - closed on mobile, open on desktop
    if (typeof window !== "undefined") {
      return window.innerWidth < 768;
    }
    return false; // Default to open on server-side rendering
  });
  const [currentAnimation, setCurrentAnimation] = useState(null);

  // Use external collapsed state if provided, otherwise use internal state
  const isCollapsed = collapsed !== undefined ? collapsed : internalCollapsed;
  const setIsCollapsed = setCollapsed || setInternalCollapsed;

  // Update collapsed state when mobile state changes (only for internal state)
  useEffect(() => {
    if (collapsed === undefined) {
      // On mobile, always keep collapsed. On desktop, start expanded
      setInternalCollapsed(isMobile);
    }
  }, [isMobile, collapsed]);

  const toggleExpand = (section) => {
    setExpandedItems((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const cameraViews = [
    { id: "front", name: "Front" },
    { id: "top", name: "Top" },
    { id: "side", name: "Side" },
    { id: "perspective", name: "Perspective" },
  ];

  // Handle object selection with shift key for multi-select
  const handleObjectSelect = (object, event) => {
    if (event && event.shiftKey) {
      // If shift key is pressed, handle multi-selection
      // Ensure selectedObjects is always an array
      const objectsArray = Array.isArray(selectedObjects)
        ? selectedObjects
        : [selectedObjects].filter(Boolean);

      if (objectsArray.includes(object)) {
        // If already selected, remove it
        onSelectObject(objectsArray.filter((obj) => obj !== object));
      } else {
        // Otherwise add it to selection
        onSelectObject([...objectsArray, object]);
      }
    } else {
      // If no shift key, just select this object
      onSelectObject([object]);
    }
  };

  const handlePlayAnimation = (animationId) => {
    if (currentAnimation === animationId) {
      setCurrentAnimation(null);
      onPlayAnimation(null);
    } else {
      setCurrentAnimation(animationId);
      onPlayAnimation(animationId);
    }
  };

  return (
    <div
      className={`flex flex-col text-[#FDE9CE] transition-all duration-300 ease-in-out max-w-[260px]
      ${isCollapsed ? "h-fit" : "h-[500px]"}
      ${isCollapsed ? "w-[100px]" : "w-[260px]"}`}
    >
      <div
        className={`flex items-center cursor-pointer ${
          isCollapsed ? "p-2 justify-center" : "p-3 justify-between "
        } 
          
          ${
            isCollapsed ? "rounded-lg" : "rounded-t-lg"
          } transition-all duration-300`}
        onClick={toggleCollapse}
      >
        {isCollapsed ? (
          <Image
            src="/images/Outliner Icon.svg"
            alt="Outliner"
            width={32}
            height={32}
            className="text-center"
          />
        ) : (
          <h3 className="text-lg font-semibold text-[#FDE9CE] flex items-center gap-2">
            <ChevronDown size={16} className="text-[#FDE9CE]" />
            Scene Outliner
          </h3>
        )}
      </div>

      {/* Content container with transition */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out flex-1
          ${isCollapsed ? "max-h-0 opacity-0" : "opacity-100"}`}
      >
        <div className="p-3 mt-[-.5rem] h-full overflow-y-auto touch-auto overscroll-contain">
          <img
            src="/images/—Pngtree—elegant gold line border_8053691 (1) 2.png"
            alt="Outliner"
            className="w-full h-auto"
          />

          {/* Scene Objects Section */}
          {sceneObjects && sceneObjects.length > 0 && (
            <div className="mb-4">
              <div
                className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
                onClick={() => toggleExpand("objects")}
              >
                <span className="text-xs md:text-sm">Scene Objects</span>
                <div className="flex items-center gap-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectObject(sceneObjects);
                    }}
                    className="text-[10px] md:text-xs px-1 bg-blue-500/30 rounded hover:bg-blue-500 hover:text-white"
                    title="Select All"
                  >
                    All
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSelectObject([]);
                    }}
                    className="text-[10px] md:text-xs px-1 bg-red-500/30 rounded hover:bg-red-500 hover:text-white"
                    title="Clear Selection"
                  >
                    Clear
                  </button>
                  <span className="text-[10px] md:text-xs ml-1">
                    {expandedItems.objects ? (
                      <ChevronDown size={14} />
                    ) : (
                      <ChevronRight size={14} />
                    )}
                  </span>
                </div>
              </div>

              {expandedItems.objects && (
                <div className="flex flex-col gap-1 p-2 max-h-[150px] overflow-y-auto touch-auto overscroll-contain">
                  {sceneObjects.map((object, index) => (
                    <div
                      key={index}
                      className={`flex items-center gap-2 p-1 rounded cursor-pointer ${
                        selectedObjects.includes(object)
                          ? "bg-blue-500/30"
                          : "hover:bg-white/10"
                      }`}
                      onClick={(e) => handleObjectSelect(object, e)}
                    >
                      <span className="text-[10px] md:text-xs">
                        {selectedObjects.includes(object) ? "●" : "○"}
                      </span>
                      <span className="text-xs md:text-sm truncate">
                        {object.name || `Object ${index + 1}`}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Camera Views Section */}
          <div className="mb-4">
            <div
              className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
              onClick={() => toggleExpand("cameras")}
            >
              <span className="text-xs md:text-sm font-medium">Cameras</span>
              <span className="text-[10px] md:text-xs">
                {expandedItems.cameras ? (
                  <ChevronDown size={14} />
                ) : (
                  <ChevronRight size={14} />
                )}
              </span>
            </div>

            {expandedItems.cameras && (
              <div className="flex flex-col gap-1 p-2">
                <div className="grid grid-cols-2 gap-2">
                  {cameraViews.map((view) => (
                    <button
                      key={view.id}
                      onClick={() => onCameraView && onCameraView(view.id)}
                      className="p-1 text-xs md:text-sm bg-transparent border border-[#FDE9CE]/30 rounded hover:bg-white/10"
                    >
                      {view.name}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Ground Section */}
          <div className="mb-4">
            <div
              className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
              onClick={() => toggleExpand("ground")}
            >
              <span className="text-xs md:text-sm font-medium">Platform</span>
              <span className="text-[10px] md:text-xs">
                {expandedItems.ground ? (
                  <ChevronDown size={14} />
                ) : (
                  <ChevronRight size={14} />
                )}
              </span>
            </div>

            {expandedItems.ground && (
              <div className="flex flex-col gap-1 p-2">
                <select
                  value={groundType}
                  onChange={(e) => setGroundType(e.target.value)}
                  className="w-full bg-gray-800 border border-gray-700 rounded px-2 py-1 text-xs md:text-sm text-[#FDE9CE]"
                >
                  <option value="reflective">Reflective</option>
                  <option value="backplate">Backplate</option>
                  <option value="none">No Ground</option>
                  <option value="grid">Grid</option>
                  <option value="solid">Solid</option>
                </select>
              </div>
            )}
          </div>

          {/* Animations Section */}
          <div className="mb-4">
            <div
              className="flex items-center justify-between p-2 border-b border-[#FDE9CE]/30 cursor-pointer"
              onClick={() => toggleExpand("animations")}
            >
              <span className="text-xs md:text-sm font-medium">Animations</span>
              <span className="text-[10px] md:text-xs">
                {expandedItems.animations ? (
                  <ChevronDown size={14} />
                ) : (
                  <ChevronRight size={14} />
                )}
              </span>
            </div>

            {expandedItems.animations && (
              <div className="flex flex-col gap-1 p-2">
                {predefinedAnimations.map((animation) => (
                  <div
                    key={animation.id}
                    className={`flex flex-col p-2 rounded cursor-pointer ${
                      currentAnimation === animation.id
                        ? "bg-blue-500/30"
                        : "hover:bg-white/10"
                    }`}
                    onClick={() => handlePlayAnimation(animation.id)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-xs md:text-sm font-medium">
                        {animation.name}
                      </span>
                      <span className="flex items-center justify-center w-5 h-5 rounded-full">
                        {currentAnimation === animation.id ? (
                          <Pause size={16} className="text-[#FDE9CE]" />
                        ) : (
                          <Play size={16} className="text-[#FDE9CE]" />
                        )}
                      </span>
                    </div>
                    <span className="text-[10px] md:text-xs text-gray-400">
                      {animation.description}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
