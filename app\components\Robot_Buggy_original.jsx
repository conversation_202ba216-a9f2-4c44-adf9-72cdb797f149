/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.3 public/models/Robot_Buggy1.glb 
*/

import React from "react";
import { useGLTF } from "@react-three/drei";

export function Model2(props) {
  const { nodes, materials } = useGLTF("/models/Robot_Buggy_original.glb");
  return (
    <group {...props} dispose={null}>
      <mesh
        geometry={nodes.Cube136.geometry}
        material={materials.LawnMower_UV01}
        position={[0, -0.101, 0.655]}
        scale={0.502}
      />
      <mesh
        geometry={nodes["1001"].geometry}
        material={materials["Patrol_Decals.004"]}
        position={[0.27, 0.438, 0.096]}
        rotation={[0.024, 0.044, 0.328]}
        scale={0.502}
      />
      <mesh
        geometry={nodes["1002"].geometry}
        material={materials["Patrol_Decals.004"]}
        position={[0.27, 0.438, 0.096]}
        rotation={[0.024, 0.044, 0.328]}
        scale={0.502}
      />
      <mesh
        geometry={nodes["1030"].geometry}
        material={materials["Trim.001"]}
        position={[0, 0.153, 0.196]}
        rotation={[Math.PI, 0, Math.PI]}
        scale={0.502}
      />
      <mesh
        geometry={nodes["1031"].geometry}
        material={materials["Trim.001"]}
        position={[0, 0.415, -0.507]}
        scale={0.502}
      />
      <mesh
        geometry={nodes["1032"].geometry}
        material={materials["Trim.001"]}
        position={[0, 0.415, -0.507]}
        scale={0.502}
      />
      <mesh
        geometry={nodes["1033"].geometry}
        material={materials["Trim.001"]}
        position={[0.431, 0.224, 0.084]}
        rotation={[2.712, -1.36, 2.72]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube044.geometry}
        material={materials.LawnMower_UV01}
        position={[0.474, 0.205, -0.063]}
        rotation={[0, -0.191, 0.175]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube052.geometry}
        material={materials["LawnMower_UV03.001"]}
        position={[0, 0, -0.091]}
      />
      <mesh
        geometry={nodes.Cube053.geometry}
        material={materials["Patrol_Decals.004"]}
        position={[0.003, -0.013, -0.092]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube056.geometry}
        material={materials["Patrol_Decals.004"]}
        position={[0, 0.006, -0.091]}
      />
      <mesh
        geometry={nodes.Cube133.geometry}
        material={materials["LawnMower_UV03.001"]}
        position={[0, -0.123, -2.825]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube134.geometry}
        material={materials.LawnMower_UV01}
        position={[0, 0.098, -0.232]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube137.geometry}
        material={materials.LawnMower_UV01}
        position={[0, -0.006, -0.095]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube138.geometry}
        material={materials.LawnMower_UV01}
        position={[0, 0, -0.091]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube139.geometry}
        material={materials["LawnMower_UV02.001"]}
        position={[0, -0.006, -0.13]}
        scale={0.179}
      />
      <mesh
        geometry={nodes.Cube140.geometry}
        material={materials.LawnMower_UV01}
        position={[0, -0.088, -0.079]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube141.geometry}
        material={materials["LawnMower_UV02.001"]}
        position={[0, 0, -0.085]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube142.geometry}
        material={materials["LawnMower_UV02.002"]}
        position={[0, 0, -0.091]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube144.geometry}
        material={materials["Trim.001"]}
        position={[0, 0, -0.091]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cube145.geometry}
        material={materials["Trim.001"]}
        position={[0, 0, -0.085]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder005.geometry}
        material={materials["Trim.001"]}
        position={[0.487, 0.088, 0.442]}
        rotation={[-1.395, 0, 0]}
        scale={0.462}
      />
      <mesh
        geometry={nodes.Cylinder006.geometry}
        material={materials["Trim.001"]}
        position={[0.629, 0.123, -0.775]}
        rotation={[-1.395, 0, 0]}
        scale={0.44}
      />
      <mesh
        geometry={nodes.Cylinder007.geometry}
        material={materials["Trim.001"]}
        position={[0.656, 0.123, -0.775]}
        rotation={[-1.395, 0, 0]}
        scale={[0.403, 0.533, 0.533]}
      />
      <mesh
        geometry={nodes.Cylinder045.geometry}
        material={materials["Trim.002"]}
        position={[0, -0.058, -0.335]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder046.geometry}
        material={materials["Trim.001"]}
        position={[0.611, 0.123, -0.774]}
        scale={[0.675, 0.54, 0.54]}
      />
      <mesh
        geometry={nodes.Cylinder047.geometry}
        material={materials.LawnMower_UV01}
        position={[0.333, 0.449, -0.992]}
        rotation={[0, -1.571, 0]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder048.geometry}
        material={materials.LawnMower_UV01}
        position={[-0.332, 0.423, -0.992]}
        rotation={[0, -1.571, 0]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder049.geometry}
        material={materials.LawnMower_UV01}
        position={[0.255, 0.189, 0.433]}
        rotation={[0, 0.147, 0]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder050.geometry}
        material={materials.LawnMower_UV01}
        position={[0.32, 0.606, -0.382]}
        rotation={[0.262, 0, 0]}
        scale={0.37}
      />
      <mesh
        geometry={nodes.Cylinder051.geometry}
        material={materials["LawnMower_UV02.001"]}
        position={[0, 0.512, -0.107]}
        rotation={[0.262, 0, 0]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder052.geometry}
        material={materials["LawnMower_UV02.001"]}
        position={[0.32, 0.612, -0.381]}
        rotation={[0.262, 0, 0]}
        scale={0.502}
      />
      <mesh
        geometry={nodes.Cylinder053.geometry}
        material={materials["LawnMower_UV03.001"]}
        position={[0.671, 0.148, -0.759]}
        scale={0.532}
      />
      <mesh
        geometry={nodes.Cylinder056.geometry}
        material={materials["Trim.003"]}
        position={[0.488, 0.088, 0.442]}
        scale={0.462}
      />
      <mesh
        geometry={nodes.Cube105.geometry}
        material={materials.LawnMower_UV01}
        position={[0, 0, -0.094]}
      />
    </group>
  );
}
