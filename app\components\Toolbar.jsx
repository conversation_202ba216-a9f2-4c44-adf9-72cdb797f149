"use client";

import { useState, useEffect, useRef } from "react";

export default function Toolbar({
  undoAction,
  redoAction,
  transformMode,
  setTransformMode,
  rightPanelTab,
  setRightPanelTab,
  sceneObjects,
  setSelectedObjects,
  selectedObjects,
  orientation = "vertical",
  panelRef,
  isMobile,
  setOutlinerCollapsed,
}) {
  const toolbarRef = useRef(null);

  // Handle button click to toggle panels exclusively
  const handlePanelToggle = (panel) => (e) => {
    e.stopPropagation();

    // Toggle panel open/closed state
    if (rightPanelTab === panel) {
      setRightPanelTab(null); // Close if already open
    } else {
      setRightPanelTab(panel); // Open the selected panel

      // If this is a new panel opening and no objects are selected, select all objects
      if (selectedObjects.length === 0 && sceneObjects.length > 0) {
        setSelectedObjects([...sceneObjects]);
      }

      // Close outliner on mobile when opening a panel
      if (isMobile && setOutlinerCollapsed) {
        setOutlinerCollapsed(true);
      }
    }
  };

  // Add document-level click handler to close panels when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Only close if a panel is open and the click is outside both the toolbar and the panel
      if (
        rightPanelTab &&
        toolbarRef.current &&
        !toolbarRef.current.contains(event.target) &&
        (!panelRef.current ||
          (panelRef.current && !panelRef.current.contains(event.target)))
      ) {
        setRightPanelTab(null);
      }
    };

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [rightPanelTab, setRightPanelTab, panelRef]);

  const isActive = (tab) => rightPanelTab === tab;

  const getButtonClass = (tab) => {
    return `p-2 rounded-lg transition-colors ${
      isActive(tab)
        ? "bg-gradient-to-tl from-[#32343D] to-[#14192D] border border-[#A3A3A3]"
        : "bg-black/30 backdrop-blur-sm hover:bg-black/50"
    }`;
  };

  const containerClass =
    orientation === "vertical"
      ? "flex flex-col space-y-4 p-3 rounded-lg"
      : "flex items-center space-x-3";

  return (
    <div className="relative" ref={toolbarRef}>
      <div className={containerClass}>
        {/* Transform button */}
        <button
          onClick={handlePanelToggle("transform")}
          className={getButtonClass("transform")}
          title="Transform Tools"
        >
          <img
            src="/images/Group 321.svg"
            alt="Transform"
            className="h-6 w-6"
          />
        </button>

        {/* Material button */}
        <button
          onClick={handlePanelToggle("material")}
          className={getButtonClass("material")}
          title="Material Panel"
        >
          <img src="/images/Group 322.svg" alt="Material" className="h-6 w-6" />
        </button>

        {/* Lights button */}
        <button
          onClick={handlePanelToggle("lights")}
          className={getButtonClass("lights")}
          title="Lights Panel"
        >
          <img src="/images/Group 323.svg" alt="Lights" className="h-6 w-6" />
        </button>

        {/* Environment button */}
        <button
          onClick={handlePanelToggle("environment")}
          className={getButtonClass("environment")}
          title="Environment Panel"
        >
          <img
            src="/images/Group 326.svg"
            alt="Environment"
            className="h-6 w-6"
          />
        </button>

        {/* Post Processing button */}
        <button
          onClick={handlePanelToggle("postprocessing")}
          className={getButtonClass("postprocessing")}
          title="Post Processing Panel"
        >
          <img
            src="/images/Group 324.svg"
            alt="Post Processing"
            className="h-6 w-6"
          />
        </button>

        {/* Engraving button */}
        <button
          onClick={handlePanelToggle("engraving")}
          className={getButtonClass("engraving")}
          title="Engraving Panel"
        >
          <img
            src="/images/engraving.svg"
            alt="Engraving"
            className="h-6 w-6"
          />
        </button>
      </div>
    </div>
  );
}
