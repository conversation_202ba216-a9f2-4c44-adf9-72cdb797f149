"use client";

import * as THREE from "three";
import { useState } from "react";
import { ChevronDown, ChevronRight, Eye, EyeOff } from "lucide-react";
import { useHistoryDebounce } from "../hooks/useDebounce";

export function LightPanel({
  lights,
  setLights,
  lightRefs,
  showGrid,
  setShowGrid,
  wireframe,
  setWireframe,
  showLightSpheres,
  setShowLightSpheres,
  onResetLights,
  addToHistory,
}) {
  const lightTypes = ["ambient", "directional", "point", "spot"];
  const [expandedLights, setExpandedLights] = useState({});
  const [expandedSections, setExpandedSections] = useState({
    visualization: true,
    addLights: true,
  });

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    300
  );

  const toggleLightExpand = (id) => {
    setExpandedLights((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const getDefaultLightProps = (type) => {
    switch (type) {
      case "directional":
        return {
          id: `directional-${Date.now()}`,
          type: "directional",
          position: [1, 1, 1],
          intensity: 0.5,
          color: "#ffffff",
          castShadow: true,
          helperVisible: true,
        };
      case "ambient":
        return {
          id: `ambient-${Date.now()}`,
          type: "ambient",
          intensity: 1,
          color: "#ffffff",
        };
      case "spot":
        return {
          id: `spot-${Date.now()}`,
          type: "spot",
          position: [0, 4, 0],
          intensity: 5,
          color: "#1fc600",
          castShadow: true,
          helperVisible: true,
          angle: Math.PI / 3,
          penumbra: 0.5,
          decay: 0,
          distance: 0,
        };
      case "point":
        return {
          id: `point-${Date.now()}`,
          type: "point",
          position: [0, 2, 0],
          intensity: 1,
          distance: 0,
          decay: 2,
          color: "#bdcbaa",
          castShadow: true,
          helperVisible: true,
          helperSize: 0.5,
        };
      default:
        return {
          id: Date.now(),
          type,
          visible: true,
        };
    }
  };

  return (
    <div className="w-full max-h-[80vh] overflow-y-auto text-[10px] md:text-xs p-2 bg-gray-900/80 rounded-lg text-gray-200">
      {/* Visualization Section */}
      <div className="mb-3">
        <button
          onClick={() => toggleSection("visualization")}
          className="w-full flex items-center justify-between text-xs md:text-sm font-medium text-gray-300 border-b border-gray-700 pb-2"
        >
          Light Visualization
          {expandedSections.visualization ? (
            <ChevronDown size={16} />
          ) : (
            <ChevronRight size={16} />
          )}
        </button>

        {expandedSections.visualization && (
          <div className="grid grid-cols-2 gap-2 mt-2">
            <label className="flex items-center gap-1 hover:text-white transition-colors">
              <input
                type="checkbox"
                checked={showLightSpheres}
                onChange={(e) => setShowLightSpheres(e.target.checked)}
                className="rounded text-blue-500"
              />
              Light Indicators
            </label>

            <label className="flex items-center gap-1 hover:text-white transition-colors">
              <input
                type="checkbox"
                checked={wireframe}
                onChange={(e) => setWireframe(e.target.checked)}
                className="rounded text-blue-500"
              />
              Wireframe
            </label>
          </div>
        )}
      </div>

      {/* Add Lights Section */}
      <div className="mb-3">
        <button
          onClick={() => toggleSection("addLights")}
          className="w-full flex items-center justify-between text-xs md:text-sm font-medium text-gray-300 border-b border-gray-700 pb-2"
        >
          Add Lights
          {expandedSections.addLights ? (
            <ChevronDown size={16} />
          ) : (
            <ChevronRight size={16} />
          )}
        </button>

        {expandedSections.addLights && (
          <div className="flex gap-1 flex-wrap mt-2">
            {lightTypes.map((type) => (
              <button
                key={type}
                onClick={() =>
                  setLights((prev) => [...prev, getDefaultLightProps(type)])
                }
                className="px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white cursor-pointer"
              >
                {type}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Lights Controls */}
      {lightTypes.map((type) => {
        const lightsOfType =
          lights?.filter((light) => light.type === type) || [];
        if (lightsOfType.length === 0) return null;

        return (
          <div key={type} className="mb-3">
            <div className="text-xs md:text-sm font-medium text-gray-300 border-b border-gray-700 pb-2">
              {type} Lights ({lightsOfType.length})
            </div>
            <div className="mt-2 space-y-2">
              {lightsOfType.map((light) => (
                <div
                  key={light.id}
                  className="p-2 bg-gray-800 rounded-lg shadow-sm"
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1 hover:text-white">
                      <button
                        onClick={() => toggleLightExpand(light.id)}
                        className="flex items-center"
                      >
                        {expandedLights[light.id] ? (
                          <ChevronDown size={14} />
                        ) : (
                          <ChevronRight size={14} />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          const newVisible = !(light.visible ?? true);
                          if (lightRefs.current[light.id]) {
                            lightRefs.current[light.id].visible = newVisible;
                          }
                          setLights((prev) =>
                            prev.map((l) =>
                              l.id === light.id
                                ? { ...l, visible: newVisible }
                                : l
                            )
                          );
                          if (addToHistory) addToHistoryImmediate();
                        }}
                        className="mr-1 text-blue-500 hover:text-blue-400"
                      >
                        {light.visible === false ? (
                          <EyeOff size={14} />
                        ) : (
                          <Eye size={14} />
                        )}
                      </button>
                      <span
                        onClick={() => toggleLightExpand(light.id)}
                        className="cursor-pointer"
                      >
                        {light.name ||
                          `${type.charAt(0).toUpperCase() + type.slice(1)} ${
                            lightsOfType.indexOf(light) + 1
                          }`}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        defaultValue={"#ffffff"}
                        onChange={(e) => {
                          if (lightRefs.current[light.id]) {
                            lightRefs.current[light.id].color = new THREE.Color(
                              e.target.value
                            );
                          }
                          if (addToHistory) addToHistoryImmediate();
                        }}
                        className="bg-transparent border-0 h-6 w-6 rounded overflow-hidden cursor-pointer"
                      />
                      <button
                        onClick={() =>
                          setLights((prev) =>
                            prev.filter((l) => l.id !== light.id)
                          )
                        }
                        className="px-1.5 py-0.5 bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-600/30 rounded"
                      >
                        ×
                      </button>
                    </div>
                  </div>

                  {expandedLights[light.id] && (
                    <div className="grid gap-2 mt-2">
                      {type !== "ambient" && (
                        <>
                          <div>
                            <label className="text-[10px] md:text-xs text-gray-400">
                              Position
                            </label>
                            <div className="space-y-2">
                              {["x", "y", "z"].map((axis) => (
                                <div
                                  key={axis}
                                  className="flex items-center gap-2"
                                >
                                  <span className="w-6 text-gray-400 font-medium">
                                    {axis.toUpperCase()}
                                  </span>
                                  <input
                                    type="range"
                                    min="-5"
                                    max="5"
                                    step="0.01"
                                    value={
                                      lightRefs.current[light.id]?.position[
                                        axis
                                      ] || 0
                                    }
                                    onChange={(e) => {
                                      if (lightRefs.current[light.id]) {
                                        lightRefs.current[light.id].position[
                                          axis
                                        ] = parseFloat(e.target.value);
                                        setLights((prev) => {
                                          const updated = prev.map((l) => {
                                            if (l.id === light.id) {
                                              return {
                                                ...l,
                                                position: [
                                                  ...lightRefs.current[light.id]
                                                    .position,
                                                ],
                                              };
                                            }
                                            return l;
                                          });
                                          if (addToHistory)
                                            addToHistoryDebounced();
                                          return updated;
                                        });
                                      }
                                    }}
                                    className="w-full accent-blue-500"
                                  />
                                  <input
                                    type="number"
                                    min="-10"
                                    max="10"
                                    step="0.1"
                                    value={
                                      lightRefs.current[light.id]?.position[
                                        axis
                                      ] || 0
                                    }
                                    onChange={(e) => {
                                      const value =
                                        parseFloat(e.target.value) || 0;
                                      if (lightRefs.current[light.id]) {
                                        lightRefs.current[light.id].position[
                                          axis
                                        ] = value;
                                        setLights((prev) => {
                                          const updated = prev.map((l) => {
                                            if (l.id === light.id) {
                                              return {
                                                ...l,
                                                position: [
                                                  ...lightRefs.current[light.id]
                                                    .position,
                                                ],
                                              };
                                            }
                                            return l;
                                          });
                                          if (addToHistory)
                                            addToHistoryImmediate();
                                          return updated;
                                        });
                                      }
                                    }}
                                    className="w-14 bg-gray-800 border border-gray-700 rounded px-1 py-0.5 text-right"
                                  />
                                </div>
                              ))}
                            </div>
                          </div>

                          {(type === "directional" ||
                            type === "spot" ||
                            type === "point") && (
                            <div>
                              <label className="flex items-center gap-1 hover:text-white text-[10px] md:text-xs">
                                <input
                                  type="checkbox"
                                  checked={light.helperVisible}
                                  onChange={(e) => {
                                    if (
                                      lightRefs.current[light.id] &&
                                      type === "point" &&
                                      lightRefs.current[light.id].userData
                                        .helper
                                    ) {
                                      lightRefs.current[
                                        light.id
                                      ].userData.helper.visible =
                                        e.target.checked;
                                    }
                                    setLights((prev) => {
                                      const updated = prev.map((l) => {
                                        if (l.id === light.id) {
                                          return {
                                            ...l,
                                            helperVisible: e.target.checked,
                                          };
                                        }
                                        return l;
                                      });
                                      if (addToHistory) addToHistoryDebounced();
                                      return updated;
                                    });
                                  }}
                                  className="rounded text-blue-500"
                                />
                                Helper
                              </label>
                            </div>
                          )}

                          {type === "spot" && (
                            <>
                              <div>
                                <label className="text-[10px] md:text-xs text-gray-400">
                                  Angle
                                </label>
                                <div className="flex items-center gap-1">
                                  <input
                                    type="range"
                                    min="1"
                                    max="6"
                                    step="0.001"
                                    value={
                                      Math.PI / (light.angle || Math.PI / 3)
                                    }
                                    onChange={(e) => {
                                      const newAngle =
                                        Math.PI / parseFloat(e.target.value);
                                      if (lightRefs.current[light.id]) {
                                        lightRefs.current[light.id].angle =
                                          newAngle;
                                      }
                                      setLights((prev) => {
                                        const updated = prev.map((l) => {
                                          if (l.id === light.id) {
                                            return {
                                              ...l,
                                              angle: newAngle,
                                            };
                                          }
                                          return l;
                                        });
                                        if (addToHistory)
                                          addToHistoryDebounced();
                                        return updated;
                                      });
                                    }}
                                    className="w-full accent-blue-500"
                                  />
                                  <span className="w-10 text-right text-[10px] md:text-xs text-gray-300">
                                    {(
                                      Math.PI / (light.angle || Math.PI / 3)
                                    ).toFixed(1)}
                                  </span>
                                </div>
                              </div>
                              <div>
                                <label className="text-[10px] md:text-xs text-gray-400">
                                  Penumbra
                                </label>
                                <div className="flex items-center gap-1">
                                  <input
                                    type="range"
                                    min="0"
                                    max="1"
                                    step="0.001"
                                    value={light.penumbra || 0.5}
                                    onChange={(e) => {
                                      const value = parseFloat(e.target.value);
                                      if (lightRefs.current[light.id]) {
                                        lightRefs.current[light.id].penumbra =
                                          value;
                                      }
                                      setLights((prev) => {
                                        const updated = prev.map((l) => {
                                          if (l.id === light.id) {
                                            return {
                                              ...l,
                                              penumbra: value,
                                            };
                                          }
                                          return l;
                                        });
                                        if (addToHistory)
                                          addToHistoryDebounced();
                                        return updated;
                                      });
                                    }}
                                    className="w-full accent-blue-500"
                                  />
                                  <span className="w-10 text-right text-[10px] md:text-xs text-gray-300">
                                    {(light.penumbra || 0.5).toFixed(1)}
                                  </span>
                                </div>
                              </div>
                            </>
                          )}
                        </>
                      )}

                      <div>
                        <label className="text-[10px] md:text-xs text-gray-400">
                          Intensity
                        </label>
                        <div className="flex items-center gap-2">
                          <input
                            type="range"
                            min="0"
                            max={type === "ambient" ? 10 : 20}
                            step="0.1"
                            value={lightRefs.current[light.id]?.intensity || 1}
                            onChange={(e) => {
                              if (lightRefs.current[light.id]) {
                                const value = parseFloat(e.target.value);
                                lightRefs.current[light.id].intensity = value;
                                setLights((prev) => {
                                  const updated = prev.map((l) =>
                                    l.id === light.id
                                      ? { ...l, intensity: value }
                                      : l
                                  );
                                  if (addToHistory) addToHistoryDebounced();
                                  return updated;
                                });
                              }
                            }}
                            className="w-full accent-blue-500"
                          />
                          <input
                            type="number"
                            min="0"
                            step="0.1"
                            value={lightRefs.current[light.id]?.intensity || 1}
                            onChange={(e) => {
                              if (lightRefs.current[light.id]) {
                                const value = parseFloat(e.target.value) || 0;
                                lightRefs.current[light.id].intensity = value;
                                setLights((prev) => {
                                  const updated = prev.map((l) =>
                                    l.id === light.id
                                      ? { ...l, intensity: value }
                                      : l
                                  );
                                  if (addToHistory) addToHistoryImmediate();
                                  return updated;
                                });
                              }
                            }}
                            className="w-16 bg-gray-800 border border-gray-700 rounded px-1 py-0.5 text-right"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        );
      })}

      {/* Reset to Default Button */}
      <div className="mt-4 pt-3 border-t border-gray-700 flex justify-center">
        <button
          onClick={onResetLights}
          className="px-3 py-1.5 bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-600/30 rounded text-[10px] md:text-xs transition-colors duration-200 font-medium"
        >
          Reset to Default
        </button>
      </div>
    </div>
  );
}
