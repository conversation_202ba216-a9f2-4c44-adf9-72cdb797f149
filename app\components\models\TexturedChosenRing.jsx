import React, { useRef, forwardRef, useEffect, useMemo, useState } from "react";
import { useGLTF, Decal } from "@react-three/drei";
import * as THREE from "three";
import { RGBELoader, EXRLoader } from "three-stdlib";
import { splitIntoConnectedGeometries } from "@/lib/seperateDiamonds.js";
import { NormalCube } from "../../utils/refraction_materials/NormalCube.js";
import { useLoader, useThree } from "@react-three/fiber";
import { DiamondShader } from "../../utils/refraction_materials/Diamond_Shader.js";
import { EngravingMaterial } from "../../utils/TextBumpMapGenerator.jsx";

export const TexturedChosenRing = forwardRef((props, ref) => {
  const { nodes, materials } = useGLTF("/models/Ring_Showcase.glb");
  const { gl: renderer } = useThree();
  const instancedMeshRef = useRef();

  // Load HDRIs for advanced materials
  const hd = useLoader(RGBELoader, "/hdris/diamond.hdr");
  const hd2 = useLoader(EXRLoader, "/hdris/env_gem_002_30251392af.exr");

  // Split the diamond into individual geometries if it exists
  const diamondGeometry = nodes.Showcase_Diamond?.geometry;
  const individualGeometries = useMemo(
    () =>
      diamondGeometry ? splitIntoConnectedGeometries(diamondGeometry) : [],
    [diamondGeometry]
  );

  const diamondsData = useMemo(() => {
    return individualGeometries.map((geo) => {
      geo.computeBoundingBox();
      geo.computeBoundingSphere();
      const center = new THREE.Vector3();
      geo.boundingBox.getCenter(center);
      const radius = geo.boundingSphere.radius;

      // Create a NormalCube instance for this geometry
      const normalCube = new NormalCube({ renderer, geo });

      return { geometry: geo, center, radius, normalCube };
    });
  }, [individualGeometries, renderer]);

  // Create matrix for the instance
  useEffect(() => {
    if (instancedMeshRef.current) {
      const matrix = new THREE.Matrix4();
      matrix.makeTranslation(0, 0, 0); // Position at origin
      instancedMeshRef.current.setMatrixAt(0, matrix);
      instancedMeshRef.current.instanceMatrix.needsUpdate = true;
    }
  }, []);

  // Manually trigger scene update after meshes are ready
  useEffect(() => {
    if (ref.current && props.sceneObjects !== undefined) {
      // Small delay to ensure all meshes are properly created and added to the scene
      const timer = setTimeout(() => {
        // Since handleSceneUpdate is not directly passed, we'll work around it
        // by ensuring each mesh has proper selectable userData
        ref.current.traverse((child) => {
          if (child.isMesh && !child.userData.selectable) {
            child.userData = { ...child.userData, selectable: true };
          }
        });
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [ref, props.sceneObjects, diamondsData]);

  // Manually call scene update when meshes are ready
  useEffect(() => {
    if (ref.current && props.onSceneUpdate && diamondsData.length > 0) {
      // Small delay to ensure all meshes are properly created and added to the scene
      const timer = setTimeout(() => {
        props.onSceneUpdate(ref.current);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [ref, props.onSceneUpdate, diamondsData]);

  // Log when clicked to help debug
  const handleClick = (e) => {
    console.log("Ring clicked:", e.object);
    e.stopPropagation();

    if (props.isPlacingDecal && props.onDecalPlacement) {
      props.onDecalPlacement(e);
      return; // Stop further processing if placing decal
    }

    // Ensure the event has the correct object property
    if (props.onClick) {
      // If clicking a child mesh, ensure the object ref is passed correctly
      if (e.object !== ref.current) {
        props.onClick(e);
      } else {
        // If clicking the group, create a similar event with the group as target
        const newEvent = { ...e, object: ref.current };
        props.onClick(newEvent);
      }
    }
  };

  // Create a unique key for the decal that changes when font or color changes
  const decalKey = useMemo(() => {
    return `${props.font}-${props.decalColor}-${props.engravingText}`;
  }, [props.font, props.decalColor, props.engravingText]);

  // Calculate dynamic font size based on text length
  const calculateFontSize = useMemo(() => {
    if (!props.engravingText) return 5;

    const textLength = props.engravingText.length;
    let baseFontSize = 5;

    // Adjust font size based on text length
    if (textLength > 12) {
      // For very long text, reduce font size more aggressively
      baseFontSize = Math.max(2.5, 5 - (textLength - 12) * 0.2);
    } else if (textLength > 8) {
      // For medium-long text, reduce font size moderately
      baseFontSize = Math.max(3.5, 5 - (textLength - 8) * 0.3);
    } else if (textLength > 6) {
      // For slightly long text, reduce font size slightly
      baseFontSize = Math.max(4, 5 - (textLength - 6) * 0.25);
    } else if (textLength < 3) {
      // For very short text, slightly increase font size
      baseFontSize = Math.min(6, 5 + (3 - textLength) * 0.3);
    }

    return baseFontSize;
  }, [props.engravingText]);

  return (
    <group
      ref={ref}
      {...props}
      dispose={null}
      onClick={handleClick}
      name="TexturedChosenRing"
      scale={[0.084375, 0.084375, 0.084375]}
      userData={{ selectable: true }}
      position={props.position || [0, 0, 0]}
      rotation={props.rotation || [0, 0, 0]}
    >
      {/* White Gold Part */}
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Mesh_0003_1?.geometry}
        name="Ring_Rose"
        onClick={handleClick}
        userData={{ selectable: true, part: "white" }}
      >
        {/* Use grey default material - premium materials are applied via MaterialPanel */}
        <meshStandardMaterial color="#cccccc" metalness={0.5} roughness={0.5} />
      </mesh>

      {/* Diamond Parts */}
      {diamondsData.length > 0 ? (
        diamondsData.map((data, index) => (
          <mesh
            key={index}
            castShadow
            receiveShadow
            geometry={data.geometry}
            name={`gem_part_${index}`}
            onClick={handleClick}
            userData={{
              selectable: true,
              part: "diamond",
            }}
          >
            {/* Conditionally render diamond shader or basic material */}
            {props.useDiamondShader ? (
              <DiamondShader
                color={"#ffffff"}
                aberrationStrength={0.01}
                toneMapped={false}
                envMap={hd}
                reflectionMap={hd2}
                normalEnvMap={data.normalCube.texture}
                diamondOriginCenter={data.center}
                diamondOriginRadius={data.radius}
                ior={2.3}
                bounces={3}
                fresnelstrength={0}
                fresnel={0.5}
                fresnelcolor={"#ffffff"}
              />
            ) : (
              <meshStandardMaterial
                color="#ffffff"
                metalness={0.1}
                roughness={0.05}
                transparent={true}
                opacity={0.9}
              />
            )}
          </mesh>
        ))
      ) : (
        // Fallback to original diamond mesh if splitting fails
        <mesh
          castShadow
          receiveShadow
          geometry={nodes.Showcase_Diamond?.geometry}
          name="Diamond"
          onClick={handleClick}
          userData={{ selectable: true, part: "diamond" }}
        >
          {/* Conditionally render diamond shader or basic material */}
          {props.useDiamondShader ? (
            <DiamondShader
              color={"#ffffff"}
              aberrationStrength={0.01}
              toneMapped={false}
              envMap={hd}
              reflectionMap={hd2}
              ior={2.3}
              bounces={3}
              fresnelstrength={0}
              fresnel={0.5}
              fresnelcolor={"#ffffff"}
            />
          ) : (
            <meshStandardMaterial
              color="#ffffff"
              metalness={0.1}
              roughness={0.05}
              transparent={true}
              opacity={0.9}
            />
          )}
        </mesh>
      )}

      {/* Rose Gold Part */}
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Mesh_0003?.geometry}
        name="Ring_White"
        onClick={handleClick}
        userData={{ selectable: true, part: "red" }}
      >
        {/* Use grey default material - premium materials are applied via MaterialPanel */}
        <meshStandardMaterial color="#dddddd" metalness={0.5} roughness={0.5} />
        {props.engravingText && (
          <Decal
            key={decalKey}
            position={props.decalPosition || [0, 1.68, 0]}
            rotation={props.decalRotation || [Math.PI / 2, 0, 0]}
            scale={props.decalScale || [6.0, 2.0, 1.5]}
            debug={props.showDecalDebug || false}
          >
            <EngravingMaterial
              text={props.engravingText}
              font={props.font}
              fontSize={calculateFontSize}
              color="#2c2c2c"
              decalColor={props.decalColor || "#000000"}
              aspect={3}
            />
          </Decal>
        )}
      </mesh>
    </group>
  );
});

TexturedChosenRing.displayName = "TexturedChosenRing";

useGLTF.preload("/models/Ring_Showcase.glb");