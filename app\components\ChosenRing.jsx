import React, { useRef, useEffect, useMemo } from "react";
import {
  useGLTF,
  useEnvironment,
  Decal,
  RenderTexture,
  Text,
  PerspectiveCamera,
} from "@react-three/drei";
import * as THREE from "three";
import { RGBELoader, EXRLoader } from "three-stdlib";
import { splitIntoConnectedGeometries } from "../../lib/seperateDiamonds.js";
import { NormalCube } from "../utils/refraction_materials/NormalCube.js";
import { useLoader, useThree } from "@react-three/fiber";
import { DiamondShader } from "../utils/refraction_materials/Diamond_Shader.js";
import { EngravingMaterial } from "../utils/TextBumpMapGenerator.jsx";

export function ChosenRing(props) {
  const groupRef = useRef();
  const { nodes, scene } = useGLTF("/models/ChosenRing1.glb");
  const { gl: renderer } = useThree();
  const diamondRef = useRef();
  const instancedMeshRef = useRef();

  const hd = useLoader(RGBELoader, "/hdris/diamond.hdr");
  const hd2 = useLoader(EXRLoader, "/hdris/env_gem_002_30251392af.exr");

  // Split the diamond into individual geometries
  const diamondGeometry = nodes.Mesh_0_1.geometry;
  const individualGeometries = useMemo(
    () => splitIntoConnectedGeometries(diamondGeometry),
    [diamondGeometry]
  );

  const diamondsData = useMemo(() => {
    return individualGeometries.map((geo) => {
      geo.computeBoundingBox();
      geo.computeBoundingSphere();
      const center = new THREE.Vector3();
      geo.boundingBox.getCenter(center);
      const radius = geo.boundingSphere.radius;

      // Create a NormalCube instance for this geometry
      const normalCube = new NormalCube({ renderer, geo });

      return { geometry: geo, center, radius, normalCube };
    });
  }, [individualGeometries, renderer]);

  // Create matrix for the instance
  useEffect(() => {
    if (instancedMeshRef.current) {
      const matrix = new THREE.Matrix4();
      matrix.makeTranslation(0, 0, 0); // Position at origin
      instancedMeshRef.current.setMatrixAt(0, matrix);
      instancedMeshRef.current.instanceMatrix.needsUpdate = true;
    }
  }, []);

  // Log when clicked to help debug
  const handleClick = (e) => {
    console.log("Ring clicked:", e.object);
    e.stopPropagation();

    if (props.isPlacingDecal && props.onDecalPlacement) {
      props.onDecalPlacement(e);
      return; // Stop further processing if placing decal
    }

    // Ensure the event has the correct object property
    if (props.onClick) {
      // If clicking a child mesh, ensure the object ref is passed correctly
      if (e.object !== groupRef.current) {
        props.onClick(e);
      } else {
        // If clicking the group, create a similar event with the group as target
        const newEvent = { ...e, object: groupRef.current };
        props.onClick(newEvent);
      }
    }
  };

  // Create a unique key for the decal that changes when font or color changes
  const decalKey = useMemo(() => {
    return `${props.font}-${props.decalColor}-${props.engravingText}`;
  }, [props.font, props.decalColor, props.engravingText]);

  // Calculate dynamic font size based on text length
  const calculateFontSize = useMemo(() => {
    if (!props.engravingText) return 5;

    const textLength = props.engravingText.length;
    let baseFontSize = 5;

    // Adjust font size based on text length
    if (textLength > 12) {
      // For very long text, reduce font size more aggressively
      baseFontSize = Math.max(2.5, 5 - (textLength - 12) * 0.2);
    } else if (textLength > 8) {
      // For medium-long text, reduce font size moderately
      baseFontSize = Math.max(3.5, 5 - (textLength - 8) * 0.3);
    } else if (textLength > 6) {
      // For slightly long text, reduce font size slightly
      baseFontSize = Math.max(4, 5 - (textLength - 6) * 0.25);
    } else if (textLength < 3) {
      // For very short text, slightly increase font size
      baseFontSize = Math.min(6, 5 + (3 - textLength) * 0.3);
    }

    return baseFontSize;
  }, [props.engravingText]);

  return (
    <group
      ref={groupRef}
      {...props}
      dispose={null}
      onClick={handleClick}
      name="ChosenRing"
      scale={[0.084375, 0.084375, 0.084375]}
      userData={{ selectable: true }}
      position={props.position || [0, 0, 0]}
      rotation={props.rotation || [0, 0, 0]}
    >
      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Mesh_0.geometry}
        name="Ring_White"
        onClick={handleClick}
        userData={{ selectable: true, part: "white" }}
      >
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={1.0}
          roughness={0.01}
          envMapIntensity={1.5}
        />
        {props.engravingText && (
          <Decal
            key={decalKey}
            position={props.decalPosition || [0, 1.68, 0]}
            rotation={props.decalRotation || [Math.PI / 2, 0, 0]}
            scale={props.decalScale || [6.0, 2.0, 1.5]}
            debug={props.showDecalDebug || false}
          >
            <EngravingMaterial
              text={props.engravingText}
              font={props.font}
              fontSize={calculateFontSize}
              color="#2c2c2c"
              decalColor={props.decalColor || "#000000"}
              aspect={3}
            />
          </Decal>
        )}
      </mesh>

      {diamondsData.map((data, index) => (
        <mesh
          key={index}
          castShadow
          receiveShadow
          geometry={data.geometry}
          name={`gem_part_${index}`}
          onClick={handleClick}
          userData={{ selectable: true, part: "diamond" }}
        >
          <DiamondShader
            color={"#ffffff"}
            aberrationStrength={0.01}
            toneMapped={false}
            envMap={hd}
            reflectionMap={hd2}
            normalEnvMap={data.normalCube.texture}
            diamondOriginCenter={data.center}
            diamondOriginRadius={data.radius}
            ior={2.3}
            bounces={3}
            fresnelstrength={0}
            fresnel={0.5}
            fresnelcolor={"#ffffff"}
          />
        </mesh>
      ))}

      <mesh
        castShadow
        receiveShadow
        geometry={nodes.Mesh_0_2.geometry}
        name="Ring_Red"
        onClick={handleClick}
        userData={{ selectable: true, part: "red" }}
      >
        <meshStandardMaterial
          color="#ffcbad"
          metalness={1.0}
          roughness={0.01}
          envMapIntensity={1.5}
        />
      </mesh>
    </group>
  );
}

useGLTF.preload("/models/ChosenRing1.glb");
