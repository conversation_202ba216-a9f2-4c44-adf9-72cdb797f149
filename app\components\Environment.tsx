import { Environment as DreiEnvironment } from "@react-three/drei";
import { useEffect, useState } from "react";
import * as THREE from "three";

// Update the preset type to match the allowed values
type PresetType =
  | "sunset"
  | "apartment"
  | "city"
  | "dawn"
  | "forest"
  | "lobby"
  | "night"
  | "park"
  | "studio"
  | "studio_small"
  | "warehouse"
  | "hdri_15"
  | "hdri_metal"
  | "none";

interface EnvironmentProps {
  preset?: PresetType; // Use the specific type instead of string
  background?: boolean;
  bgColor?: string;
  customHdri?: string | null;
  intensity?: number;
  envRotation?: number;
  blur?: number;
}

export function Environment({
  preset = "sunset",
  background = false,
  bgColor = "#000000",
  customHdri = null,
  intensity = 1,
  envRotation = 0,
  blur = 0.1,
}: EnvironmentProps) {
  const [backgroundColor, setBackgroundColor] = useState<THREE.Color | null>(
    null
  );

  useEffect(() => {
    if (bgColor) {
      setBackgroundColor(new THREE.Color(bgColor));
    }
  }, [bgColor]);

  return (
    <>
      {backgroundColor && (
        <color attach="background" args={[backgroundColor]} />
      )}

      {preset !== "none" &&
        (customHdri ? (
          <DreiEnvironment
            files={customHdri}
            background={background}
            environmentIntensity={intensity}
            environmentRotation={[0, envRotation, 0]}
            blur={background ? blur : 0}
          />
        ) : preset === "hdri_15" ? (
          <DreiEnvironment
            files="/hdris/hdri_15.hdr"
            background={background}
            environmentIntensity={intensity}
            environmentRotation={[0, envRotation, 0]}
            blur={background ? blur : 0}
          />
        ) : preset === "hdri_metal" ? (
          <DreiEnvironment
            files="/hdris/hdri_metal.exr"
            background={background}
            environmentIntensity={intensity}
            environmentRotation={[0, envRotation, 0]}
            blur={background ? blur : 0}
          />
        ) : preset === "studio_small" ? (
          <DreiEnvironment
            files="/hdris/studio_small_02_2k.hdr"
            background={background}
            environmentIntensity={intensity}
            environmentRotation={[0, envRotation, 0]}
            blur={background ? blur : 0}
          />
        ) : (
          <DreiEnvironment
            preset={
              preset as Exclude<
                PresetType,
                "studio_small" | "hdri_15" | "hdri_metal" | "none"
              >
            }
            background={background}
            environmentIntensity={intensity}
            environmentRotation={[0, envRotation, 0]}
            blur={background ? blur : 0}
          />
        ))}
    </>
  );
}
