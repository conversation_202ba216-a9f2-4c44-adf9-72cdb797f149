<div align="center">
<h1>Agape Optimized Environment - 3D Renderer
</h1>
</div>

Currently, this project is a work in progress. The goal is to create a simple, easy-to-use, and optmized environment that allows our team render 3D models very easily, and it ideally shouldn't have performance issues.
The environment is built using Next.js, JavaScript, TypeScript, React Three Fiber, React Three Drei, Rapier, and glTF-Transform (experimental).

Right now, the model in here is the Robot Buggy model from <PERSON>, but the idea is that we should be able to use any model, and render it smoothly. The model is a glb file, and it's located in the `public` folder.

## How to run locally

Follow these steps to run on your local machine.

### Clone this repository

```bash
git clone https://github.com/agapeengine/agape-optimized-environment.git
```

### Navigate to directory

```bash
cd agape-optimized-environment
```

### Install dependencies

```bash
pnpm install
```

### Run

Run the development server to view the app.

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.
