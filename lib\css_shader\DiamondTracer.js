export const vertexShader = /* glsl */ `
varying vec3 v_worldNormal;
varying vec3 v_vertexWorldPosition;
varying vec3 v_worldPosition;
varying mat4 v_viewMatrix;
varying vec2 v_uv;
varying vec3 v_normal;
uniform mat4 cameraMatrixWorld;
varying mat4 v_modelMatrix;
varying vec3 v_local_position;

void main() {
    v_uv = uv;
    mat4 modelViewMatrix = viewMatrix * modelMatrix;
    mat4 modelViewProj = projectionMatrix * modelViewMatrix;

    v_worldNormal = (modelMatrix * vec4(normal,0.0)).xyz;
    v_vertexWorldPosition = (modelMatrix * vec4(position.xyz, 1.0 )).xyz;

    v_worldPosition = v_vertexWorldPosition;
    v_viewMatrix = viewMatrix;
    v_modelMatrix = modelMatrix;
    v_local_position = position;

    // v_worldPosition = (modelMatrix * vec4(position, 1.0)).xyz;
    v_normal = (cameraMatrixWorld * vec4(normalMatrix * normal, 0.0)).xyz;
    gl_Position = modelViewProj * vec4( position.xyz, 1.0 );
}
`

export const fragmentShader = /* glsl */ `

precision highp float;

varying vec3 v_worldNormal;
varying vec3 v_vertexWorldPosition;
varying vec3 v_worldPosition;
varying mat4 v_viewMatrix;
varying vec2 v_uv;
varying vec3 v_normal;
varying mat4 v_modelMatrix;
varying vec3 v_local_position;

uniform int bounces;

uniform mat4 modelMatrix;

uniform highp samplerCube normalEnvMap;
uniform highp samplerCube colorEnvMap;
uniform float colorEnvMapRotY;
uniform float roughness;
uniform mat4 matrixWorld;
uniform float ior;
uniform float aberrationStrength;

uniform float diamondOriginRadius;
uniform vec3 diamondOriginCenter;

uniform mat4 projectionMatrixInverse;
uniform mat4 cameraMatrixWorld;
uniform vec3 offsetRainbowRedChannelNoise;
uniform vec3 offsetRainbowGreenChannelNoise;
uniform vec3 offsetRainbowBlueChannelNoise;

uniform float contrastFactor;
uniform float brightnessFactor;


mat3 rotateX(float theta) {
    float c = cos(theta);
    float s = sin(theta);
    return mat3(
        vec3(1, 0, 0),
        vec3(0, c, -s),
        vec3(0, s, c)
    );
}

mat3 rotateY(float theta) {
    float c = cos(theta);
    float s = sin(theta);
    return mat3(
        vec3(c, 0, s),
        vec3(0, 1, 0),
        vec3(-s, 0, c)
    );
}

mat3 rotateZ(float theta) {
    float c = cos(theta);
    float s = sin(theta);
    return mat3(
        vec3(c, -s, 0),
        vec3(s, c, 0),
        vec3(0, 0, 1)
    );
}

vec3 getCameraDirectionHighDetail () {
    vec3 directionCamPerfect;
    directionCamPerfect = (projectionMatrixInverse * vec4(v_uv * 2.0 - 1.0, 0.0, 1.0)).xyz;
    directionCamPerfect = (cameraMatrixWorld * vec4(directionCamPerfect, 0.0)).xyz;
    directionCamPerfect = normalize(directionCamPerfect);

    return directionCamPerfect;
}

vec4 getPackedData(vec3 n){
    vec3 directionCamPerfect = getCameraDirectionHighDetail();

    vec4 data = textureGrad(normalEnvMap, n, dFdx(directionCamPerfect), dFdy(directionCamPerfect));

    data.rgb = data.rgb * 2.0 - 1.0;
    data.rgb = normalize(-data.rgb);
    data.rgb *= -1.0;

    data.a = data.a * diamondOriginRadius;

    return data;
}

vec4 getRawData(vec3 n){
    vec3 directionCamPerfect = getCameraDirectionHighDetail();

    vec4 data = textureGrad(normalEnvMap, n, dFdx(directionCamPerfect), dFdy(directionCamPerfect));

    return data;
}

vec4 getColor(vec3 rayDir) {
    vec3 directionCamPerfect = getCameraDirectionHighDetail();

    rayDir *= rotateY(colorEnvMapRotY);

    // vec4 color = textureCube(colorEnvMap, rayDir);

    vec4 color = textureGrad(colorEnvMap, rayDir, dFdx(directionCamPerfect), dFdy(directionCamPerfect));

    return color;
}


// Calculate the specular reflection in the environment using GGX distribution
vec3 calculateSpecularGGXEnvironment(vec3 rayDirection, float roughness) {
    // Get surface normal from packed data
    vec4 info = getPackedData(rayDirection);
    vec3 surfaceNormal = info.rgb;
    
    // Set the specular color
    vec3 specularColor = vec3(1.0);
    
    // Calculate reflection direction
    vec3 reflectionDirection = reflect(-rayDirection, surfaceNormal);
    
    // Calculate dot products
    float NdotV = max(dot(surfaceNormal, rayDirection), 0.0);
    vec3 halfwayDirection = normalize(rayDirection + reflectionDirection);
    float NdotH = max(dot(surfaceNormal, halfwayDirection), 0.0);
    float VdotH = max(dot(rayDirection, halfwayDirection), 0.0);
    
    // Calculate the GGX distribution
    float roughnessSquared = roughness * roughness;
    float D = (roughnessSquared * NdotH * NdotH) / (3.14159 * pow((NdotH * NdotH) * (roughnessSquared - 1.0) + 1.0, 2.0));
    
    // Calculate the geometric term
    float G = min(1.0, (2.0 * NdotH * min(NdotV, VdotH)) / (VdotH + NdotV));
    
    // Calculate the fresnel term
    vec3 F = specularColor + (vec3(1.0) - specularColor) * pow(1.0 - VdotH, 5.0);
    
    // Calculate the specular environment
    vec3 specularEnvironment = (D * G * F) / (4.0 * NdotV * NdotH);
    
    return specularEnvironment;
}


vec3 hitSphere(vec3 origin, vec3 direction, vec3 center, float radius) {
    vec3 oc = origin - center;

    float a = dot(direction, direction);
    float b = dot(oc, direction);
    float c = dot(oc, oc) - radius * radius;
    float discriminant = b * b - a * c;

    if (discriminant > 0.0) {
        float sqrtDiscriminant = sqrt(discriminant);
        float t = (-b + sqrtDiscriminant) / a;
        return origin + direction * t;
    }

    return vec3(0.0);
}

vec3 hitPlane(vec3 linePoint, vec3 lineDir, vec3 planePoint, vec3 planeDir) {
    vec3 normalizedLineDir = normalize(lineDir);
    vec3 normalizedPlaneDir = normalize(planeDir);

    float dotProduct = dot(normalizedPlaneDir, planePoint - linePoint);
    float dotProductLine = dot(normalizedPlaneDir, normalizedLineDir);
    float howFar = dotProduct / dotProductLine;
    vec3 position = normalizedLineDir * howFar;
    vec3 intersectionPoint = linePoint + position;
    return intersectionPoint;
}

vec3 hitRay(vec3 rayOrigin, vec3 rayDirection) {
    vec3 sphereHitPoint = hitSphere(rayOrigin, rayDirection, diamondOriginCenter, diamondOriginRadius);
    vec3 sphereCenterOffset = diamondOriginCenter;
    
    // Calculate hit point and normal for the first intersection
    vec3 dir1 = normalize(sphereHitPoint - sphereCenterOffset);
    vec4 info1 = getPackedData(dir1);
    float dist1 = info1.a;
    vec3 point1 = sphereCenterOffset + dir1 * dist1;
    vec3 normal1 = info1.rgb;
    vec3 hit1 = hitPlane(rayOrigin, rayDirection, point1, normal1);

    return hit1;
}


vec3 render(vec3 origin, vec3 direction, vec3 normal, float ior) {
    mat4 modelMatrixInverse = inverse(v_modelMatrix);
    vec3 accumulatedColor = vec3(0.0);
    vec3 rayOrigin = origin;
    vec3 rayDirection = direction;

    rayDirection = refract(rayDirection, normal, 1.0 / ior);
    rayOrigin = v_worldPosition + rayDirection * 0.1;
    rayOrigin = (modelMatrixInverse * vec4(rayOrigin, 1.0)).xyz;
    rayDirection = normalize((modelMatrixInverse * vec4(rayDirection, 0.0)).xyz);

    int BOUNCES = bounces;
    for(int i = 0; i < BOUNCES; i++) {
        vec3 hitPoint = hitRay(rayOrigin, rayDirection);

        vec3 pureDirectionToRaycast = normalize(hitPoint - diamondOriginCenter);
        vec4 info = getPackedData(pureDirectionToRaycast);

        vec3 diamondWallCastNormal = info.rgb;
        vec3 diamondWallCastPoint = diamondOriginCenter + pureDirectionToRaycast * info.a;

        rayOrigin = diamondWallCastPoint;
        
        vec3 tempDir = refract(rayDirection, diamondWallCastNormal, ior);
        if (length(tempDir) != 0.0) {
            rayDirection = tempDir;
            continue;
        }

        rayDirection = reflect(rayDirection, diamondWallCastNormal);
    }

    vec3 src = normalize((inverse(v_modelMatrix) * vec4(-rayDirection, 0.0)).xyz);

    //////////
    //////////
    //////////
    //////////
    //////////

    vec3 worldRayDir1 = src;

    float enableRainbow = 0.0;
    if (length(v_local_position.xz) / diamondOriginRadius > 0.33) {
        enableRainbow = 1.0;
    }
    
    vec3 diamondReflection1 = vec3(
        getColor(normalize(worldRayDir1.rgb + enableRainbow * offsetRainbowRedChannelNoise - vec3(enableRainbow * aberrationStrength))).r,
        getColor(normalize(worldRayDir1.rgb + enableRainbow * offsetRainbowGreenChannelNoise)).g,
        getColor(normalize(worldRayDir1.rgb + enableRainbow * offsetRainbowBlueChannelNoise + vec3(enableRainbow * aberrationStrength))).b
    );
    vec3 colorOutput1 = diamondReflection1 * calculateSpecularGGXEnvironment(normalize(worldRayDir1.rgb), 0.29);

    // //////////
    // //////////
    // //////////
    // //////////
    // //////////

    vec3 worldRayDir2 = src * rotateY(3.14159265 * 0.25);
    
    vec3 diamondReflection2 = vec3(
        getColor(normalize(worldRayDir2.rgb + enableRainbow * offsetRainbowRedChannelNoise + vec3(enableRainbow * aberrationStrength))).r,
        getColor(normalize(worldRayDir2.rgb + enableRainbow * offsetRainbowGreenChannelNoise)).g,
        getColor(normalize(worldRayDir2.rgb + enableRainbow * offsetRainbowBlueChannelNoise - vec3(enableRainbow * aberrationStrength))).b
    );
    vec3 colorOutput2 = diamondReflection2 * calculateSpecularGGXEnvironment(normalize(worldRayDir2.rgb), 0.29);


    // //////////
    // //////////
    // //////////
    // //////////
    // //////////
    // //////////

    vec3 mixedColor = mix(colorOutput1, colorOutput2, 0.5);
    accumulatedColor.rgb = pow(mixedColor, vec3(1.9)) * 1.1 + 0.1;

    if (diamondOriginRadius <= 1.0) {
        accumulatedColor.rgb *= 0.9;
    }

    accumulatedColor.rgb = pow(accumulatedColor.rgb, vec3(contrastFactor));
    accumulatedColor.rgb *= brightnessFactor;

    //////////
    //////////
    //////////
    //////////
    //////////
    //////////

    gl_FragColor.rgb = accumulatedColor.rgb;
    gl_FragColor.a = 1.0;

    return vec3(0.0);
}


void main() {   
    vec3 normal = v_normal;
    vec3 rayOrigin = cameraPosition;
    vec3 rayDirection = normalize(v_worldPosition - cameraPosition);

    render(rayOrigin, rayDirection, normal, ior);
}




`
