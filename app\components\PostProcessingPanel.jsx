"use client";

import { useState } from "react";
import { useHistoryDebounce } from "../hooks/useDebounce";

export function PostProcessingPanel({
  postProcessingEnabled,
  setPostProcessingEnabled,
  postProcessingSettings,
  setPostProcessingSettings,
  addToHistory,
}) {
  const [activeTab, setActiveTab] = useState("bloom");

  // Setup debounced history for slider inputs
  const { addToHistoryImmediate, addToHistoryDebounced } = useHistoryDebounce(
    addToHistory || (() => {}),
    300
  );

  const updateSetting = (effect, property, value, isSlider = false) => {
    setPostProcessingSettings((prev) => ({
      ...prev,
      [effect]: {
        ...prev[effect],
        [property]: value,
      },
    }));

    // Add to history with appropriate debouncing
    if (addToHistory) {
      if (isSlider) {
        addToHistoryDebounced();
      } else {
        addToHistoryImmediate();
      }
    }
  };

  return (
    <div className="text-white w-full overflow-y-auto text-xs">
      <div className="mb-4">
        <div className="mb-2 border-b border-gray-600">Post Processing</div>

        <div className="mb-3">
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={postProcessingEnabled}
              onChange={(e) => {
                setPostProcessingEnabled(e.target.checked);
                if (addToHistory) {
                  addToHistoryImmediate();
                }
              }}
            />
            Enable Post Processing
          </label>
        </div>

        {postProcessingEnabled && (
          <>
            <div className="mb-3">
              <div className="flex gap-1 mb-2">
                <button
                  onClick={() => setActiveTab("bloom")}
                  className={`px-2 py-1 rounded text-xs ${
                    activeTab === "bloom" ? "bg-blue-500" : "bg-gray-700"
                  }`}
                >
                  Bloom
                </button>
                {/* <button
                  onClick={() => setActiveTab("dof")}
                  className={`px-2 py-1 rounded text-xs ${
                    activeTab === "dof" ? "bg-blue-500" : "bg-gray-700"
                  }`}
                >
                  Depth of Field
                </button> */}
                <button
                  onClick={() => setActiveTab("vignette")}
                  className={`px-2 py-1 rounded text-xs ${
                    activeTab === "vignette" ? "bg-blue-500" : "bg-gray-700"
                  }`}
                >
                  Vignette
                </button>
                <button
                  onClick={() => setActiveTab("autofocus")}
                  className={`px-2 py-1 rounded text-xs ${
                    activeTab === "autofocus" ? "bg-blue-500" : "bg-gray-700"
                  }`}
                >
                  Autofocus DOF
                </button>
              </div>
            </div>

            {activeTab === "bloom" && (
              <div>
                <div className="mb-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={postProcessingSettings.bloom.enabled}
                      onChange={(e) =>
                        updateSetting(
                          "bloom",
                          "enabled",
                          e.target.checked,
                          false
                        )
                      }
                    />
                    Enable Bloom
                  </label>
                </div>

                <div className="mb-2">
                  <label>Intensity</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="2"
                      step="0.01"
                      value={postProcessingSettings.bloom.intensity}
                      onChange={(e) =>
                        updateSetting(
                          "bloom",
                          "intensity",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.bloom.intensity.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="mb-2">
                  <label>Threshold</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={postProcessingSettings.bloom.threshold}
                      onChange={(e) =>
                        updateSetting(
                          "bloom",
                          "threshold",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.bloom.threshold.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="mb-2">
                  <label>Radius</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={postProcessingSettings.bloom.radius}
                      onChange={(e) =>
                        updateSetting(
                          "bloom",
                          "radius",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.bloom.radius.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* {activeTab === "dof" && (
              <div>
                <div className="mb-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={postProcessingSettings.dof.enabled}
                      onChange={(e) =>
                        updateSetting("dof", "enabled", e.target.checked, false)
                      }
                    />
                    Enable Depth of Field
                  </label>
                </div>

                <div className="mb-2">
                  <label>Focus Distance</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.1"
                      value={postProcessingSettings.dof.focusDistance}
                      onChange={(e) =>
                        updateSetting(
                          "dof",
                          "focusDistance",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.dof.focusDistance.toFixed(1)}
                    </span>
                  </div>
                </div>

                <div className="mb-2">
                  <label>Aperture</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="0.1"
                      step="0.001"
                      value={postProcessingSettings.dof.aperture}
                      onChange={(e) =>
                        updateSetting(
                          "dof",
                          "aperture",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.dof.aperture.toFixed(3)}
                    </span>
                  </div>
                </div>

                <div className="mb-2">
                  <label>Bokeh Scale</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="10"
                      step="0.1"
                      value={postProcessingSettings.dof.bokehScale}
                      onChange={(e) =>
                        updateSetting(
                          "dof",
                          "bokehScale",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.dof.bokehScale.toFixed(1)}
                    </span>
                  </div>
                </div>
              </div>
            )} */}

            {activeTab === "vignette" && (
              <div>
                <div className="mb-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={postProcessingSettings.vignette.enabled}
                      onChange={(e) =>
                        updateSetting(
                          "vignette",
                          "enabled",
                          e.target.checked,
                          false
                        )
                      }
                    />
                    Enable Vignette
                  </label>
                </div>

                <div className="mb-2">
                  <label>Darkness</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={postProcessingSettings.vignette.darkness}
                      onChange={(e) =>
                        updateSetting(
                          "vignette",
                          "darkness",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.vignette.darkness.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="mb-2">
                  <label>Offset</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={postProcessingSettings.vignette.offset}
                      onChange={(e) =>
                        updateSetting(
                          "vignette",
                          "offset",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.vignette.offset.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "autofocus" && (
              <div>
                <div className="mb-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={postProcessingSettings.autofocus.enabled}
                      onChange={(e) =>
                        updateSetting(
                          "autofocus",
                          "enabled",
                          e.target.checked,
                          false
                        )
                      }
                    />
                    Enable Autofocus DOF
                  </label>
                </div>

                <div className="mb-2">
                  <label>Bokeh Scale</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0"
                      max="20"
                      step="0.1"
                      value={postProcessingSettings.autofocus.bokehScale}
                      onChange={(e) =>
                        updateSetting(
                          "autofocus",
                          "bokehScale",
                          parseFloat(e.target.value),
                          true
                        )
                      }
                      className="w-full"
                    />
                    <span className="w-10 text-right">
                      {postProcessingSettings.autofocus.bokehScale.toFixed(1)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
