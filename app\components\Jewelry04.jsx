import React, { useEffect } from "react";
import { useGLTF } from "@react-three/drei";

export function Jewelry04(props) {
  const { scene } = useGLTF("/models/04.glb");

  useEffect(() => {
    scene.traverse((child) => {
      if (child.isMesh) {
        child.material.wireframe = props.wireframe;
        child.onClick = props.onClick;
        child.userData = { selectable: true };
      }
    });
    props.onLoad?.(scene);
  }, [scene, props.wireframe, props.onLoad, props.onClick]);

  return <primitive object={scene} position={props.position} />;
}

// useGLTF.preload("/models/04.glb");
