import { useRef } from "react";
import { useFrame } from "@react-three/fiber";
import { useKeyboardControls } from "@react-three/drei";
import { RigidBody, useRapier } from "@react-three/rapier";
import { Model } from "./Robot_Buggy_optimized";

export function Car() {
  const carRef = useRef();
  const { rapier } = useRapier();
  const [subscribeKeys, getKeys] = useKeyboardControls();

  const speed = useRef(0);
  const steeringAngle = useRef(0);

  useFrame((state, delta) => {
    const keys = getKeys();
    const rigidBody = carRef.current;

    if (rigidBody) {
      const maxSpeed = 15;
      const acceleration = 15;
      const deceleration = 12;
      const steeringSpeed = 1;
      const maxSteeringAngle = 0.3;

      if (keys.forward) {
        speed.current = Math.min(
          speed.current + acceleration * delta,
          maxSpeed
        );
      }
      if (keys.backward) {
        speed.current = Math.max(
          speed.current - deceleration * delta,
          -maxSpeed / 2
        );
      }
      if (keys.brake) {
        speed.current *= 0.95;
      }
      if (!keys.forward && !keys.backward) {
        speed.current *= 0.95;
      }

      if (keys.left) {
        steeringAngle.current = Math.min(
          steeringAngle.current + steeringSpeed * delta,
          maxSteeringAngle
        );
      }
      if (keys.right) {
        steeringAngle.current = Math.max(
          steeringAngle.current - steeringSpeed * delta,
          -maxSteeringAngle
        );
      }
      if (!keys.left && !keys.right) {
        steeringAngle.current *= 0.95;
      }

      // Apply forces
      const impulse = new rapier.Vector3(
        Math.sin(rigidBody.rotation().y) * speed.current,
        0,
        Math.cos(rigidBody.rotation().y) * speed.current
      );

      rigidBody.applyImpulse(impulse, true);
      rigidBody.applyTorqueImpulse(
        new rapier.Vector3(0, steeringAngle.current * speed.current, 0),
        true
      );
    }
  });

  return (
    <>
      <RigidBody
        ref={carRef}
        mass={1500}
        canSleep={false}
        linearDamping={0.8}
        angularDamping={0.9}
        position={[0, 1, 0]}
      >
        <Model />
      </RigidBody>
    </>
  );
}
