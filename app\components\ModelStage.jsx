import { Suspense, useRef, useEffect } from "react";
import { Center, Float } from "@react-three/drei";
import { PrototypeA } from "./PrototypeA";
import { ChosenRing } from "./ChosenRing";
import { ChosenRingV2 } from "./ChosenRingV2";
import { Computer } from "./Computer";
import UploadedModel from "./UploadedModel";
// import { Jewelry01 } from "./Jewelry01";
import { Jewelry02 } from "./Jewelry02";
import { Jewelry03 } from "./Jewelry03";
import { Jewelry04 } from "./Jewelry04";
import { Jewelry05 } from "./Jewelry05";
import { BackPlate } from "./BackPlate";
import { AgapeRing1 } from "./models/AgapeRing1";
import { AgapeRing2 } from "./models/AgapeRing2";
import { RingA } from "./models/RingA";
import { RingB } from "./models/RingB";
import { RingC } from "./models/RingC";
import { TestRing } from "./models/TestRing";

const MODEL_CONFIGS = {
  mech: {
    component: PrototypeA,
    environment: "city",
    intensity: 0.6,
    yOffset: -0.11,
  },

  chosenring: {
    component: ChosenRing,
    environment: "studio",
    intensity: 0.8,
    yOffset: 0.01,
    defaultDecalPosition: [0, 1.68, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [4.0, 0.5, 1.5],
  },
  testring: {
    component: TestRing,
    environment: "hdri_metal",
    intensity: 0.8,
    yOffset: 0.02,
    rotation: [Math.PI / 2.15, 0, 0],
    position: [0, 0, 1.5],
    defaultDecalPosition: [0, 1.68, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [4.0, 0.5, 1.5],
  },
  agapering1: {
    component: AgapeRing1,
    environment: "sunset",
    intensity: 0.8,
    yOffset: 0,
    defaultDecalPosition: [0, 1.2, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [2.0, 0.3, 1.0],
  },
  agapering2: {
    component: AgapeRing2,
    environment: "sunset",
    intensity: 0.8,
    yOffset: 0,
    defaultDecalPosition: [0, 1.2, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [2.0, 0.3, 1.0],
  },
  pccase: {
    component: Computer,
    environment: "warehouse",
    intensity: 0.6,
    yOffset: 0,
  },
  // jewelry01: {
  //   component: Jewelry01,
  //   environment: "studio",
  //   intensity: 0.8,
  //   yOffset: 0,
  // },
  jewelry02: {
    component: Jewelry02,
    environment: "studio",
    intensity: 0.8,
    yOffset: -0.35,
  },
  jewelry03: {
    component: Jewelry03,
    environment: "studio",
    intensity: 0.8,
    yOffset: 0,
  },
  jewelry04: {
    component: Jewelry04,
    environment: "studio",
    intensity: 0.8,
    yOffset: 0,
  },
  jewelry05: {
    component: Jewelry05,
    environment: "city",
    intensity: 1,
    yOffset: 0,
  },
  ringa: {
    component: RingA,
    environment: "city",
    intensity: 1,
    yOffset: 0,
    defaultDecalPosition: [0, 0.26, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [1.0, 0.2, 0.8],
  },
  ringb: {
    component: RingB,
    environment: "city",
    intensity: 1,
    yOffset: 0,
    defaultDecalPosition: [0, 0.5, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [1.0, 0.2, 0.8],
  },
  ringc: {
    component: RingC,
    environment: "city",
    intensity: 1,
    yOffset: 0,
    defaultDecalPosition: [0, 0.26, 0],
    defaultDecalRotation: [Math.PI / 2, 0, 0],
    defaultDecalScale: [1.0, 0.2, 0.8],
  },
};

export function ModelStage({
  selectedModel,
  wireframe,
  handleSceneUpdate,
  handleObjectClick,
  uploadedModel,
  groundType,
  engravingText,
  activeAnimation,
  decalPosition,
  decalScale,
  decalRotation,
  decalColor,
  font,
  isPlacingDecal,
  onDecalPlacement,
  showDecalDebug,
}) {
  const modelRef = useRef();
  const config = MODEL_CONFIGS[selectedModel] || {};
  const ModelComponent = config.component;

  useEffect(() => {
    if (modelRef.current) {
      handleSceneUpdate(modelRef.current);
    }
  }, [selectedModel, handleSceneUpdate]);

  if (!ModelComponent && selectedModel !== "uploaded") {
    return null;
  }

  const renderModel = () => {
    if (selectedModel === "uploaded" && uploadedModel) {
      return (
        <UploadedModel
          ref={modelRef}
          url={uploadedModel}
          wireframe={wireframe}
          onLoad={handleSceneUpdate}
          onClick={handleObjectClick}
        />
      );
    } else if (selectedModel === "chosenring") {
      return (
        <ChosenRing
          onClick={handleObjectClick}
          onLoad={handleSceneUpdate}
          wireframe={wireframe}
          engravingText={engravingText}
          decalPosition={decalPosition}
          decalScale={decalScale}
          decalRotation={decalRotation}
          decalColor={decalColor}
          font={font}
          activeAnimation={activeAnimation}
          isPlacingDecal={isPlacingDecal}
          onDecalPlacement={onDecalPlacement}
          showDecalDebug={showDecalDebug}
        />
      );
    } else if (selectedModel === "testring") {
      return (
        <ModelComponent
          ref={modelRef}
          position={config.position || [0, 0, 0]}
          rotation={config.rotation || [0, 0, 0]}
          wireframe={wireframe}
          onLoad={handleSceneUpdate}
          onClick={handleObjectClick}
          engravingText={engravingText}
          decalPosition={decalPosition}
          decalScale={decalScale}
          decalRotation={decalRotation}
          decalColor={decalColor}
          font={font}
          isPlacingDecal={isPlacingDecal}
          onDecalPlacement={onDecalPlacement}
          showDecalDebug={showDecalDebug}
        />
      );
    } else {
      // For all other rings, use default predefined positions but allow manual placement
      const useDefaultPosition =
        !isPlacingDecal &&
        decalPosition[0] === 0 &&
        decalPosition[1] === 1.68 &&
        decalPosition[2] === 0;

      return (
        <ModelComponent
          ref={modelRef}
          position={config.position || [0, 0, 0]}
          rotation={config.rotation || [0, 0, 0]}
          wireframe={wireframe}
          onLoad={handleSceneUpdate}
          onClick={handleObjectClick}
          engravingText={engravingText}
          decalPosition={
            useDefaultPosition
              ? config.defaultDecalPosition || decalPosition
              : decalPosition
          }
          decalScale={
            useDefaultPosition
              ? config.defaultDecalScale || decalScale
              : decalScale
          }
          decalRotation={
            useDefaultPosition
              ? config.defaultDecalRotation || decalRotation
              : decalRotation
          }
          decalColor={decalColor}
          font={font}
          isPlacingDecal={isPlacingDecal}
          onDecalPlacement={onDecalPlacement}
          showDecalDebug={showDecalDebug}
        />
      );
    }
  };

  return (
    <Suspense fallback={null}>
      {groundType === "backplate" && (
        <>
          <BackPlate scale={0.75} position={[0, 0.7, 0]} />
          <BackPlate
            scale={0.75}
            position={[0, 0.7, 0]}
            rotation={[0, Math.PI, 0]}
          />
        </>
      )}

      <Center
        key={`model-center-${selectedModel}`}
        top
        position={[0, config.yOffset || 0, 0]}
      >
        {activeAnimation === "float" ? (
          <Float
            speed={1.5}
            floatIntensity={1.5}
            rotationIntensity={1}
            floatingRange={[0.1, 0.25]}
          >
            {renderModel()}
          </Float>
        ) : (
          renderModel()
        )}
      </Center>
    </Suspense>
  );
}
