{"name": "agape-optimized-environment", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/runtime": "^7.27.4", "@gltf-transform/cli": "^4.1.3", "@gltf-transform/core": "^4.1.3", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@react-three/csg": "^4.0.0", "@react-three/drei": "^10.0.6", "@react-three/fiber": "9.1.2", "@react-three/postprocessing": "^3.0.4", "@react-three/rapier": "^2.1.0", "@types/react-color": "^3.0.13", "@types/three": "^0.175.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "leva": "^0.10.0", "lucide-react": "^0.488.0", "next": "15.3.0", "postprocessing": "^6.37.3", "react": "^19.1.0", "react-color": "^2.19.3", "react-dom": "^19.1.0", "react-picker": "^1.2.17", "react-toastify": "^11.0.5", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "three": "^0.175.0", "three-stdlib": "^2.36.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^22.14.1", "@types/react": "^19.1.1", "@types/react-dom": "^19.1.2", "eslint": "^9.24.0", "eslint-config-next": "15.3.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.12.1+sha512.e5a7e52a4183a02d5931057f7a0dbff9d5e9ce3161e33fa68ae392125b79282a8a8a470a51dfc8a0ed86221442eb2fb57019b0990ed24fab519bf0e1bc5ccfc4"}