import React, { forwardRef, useEffect } from "react";
import { useGLTF } from "@react-three/drei";

export const Jewelry05 = forwardRef((props, ref) => {
  const { nodes, materials } = useGLTF("/models/05.glb");

  // Set default material properties
  useEffect(() => {
    // Set metallic properties for Silver parts
    if (materials.Silver) {
      materials.Silver.metalness = 1.0;
      materials.Silver.roughness = 0.1;
      materials.Silver.envMapIntensity = 1.2;
    }

    // Set metallic properties for Prongs
    if (materials["Prongs.001"]) {
      materials["Prongs.001"].metalness = 1.0;
      materials["Prongs.001"].roughness = 0.1;
      materials["Prongs.001"].envMapIntensity = 1.2;
    }

    // Set gem-like properties for Diamond
    if (materials["Diamond.002"]) {
      materials["Diamond.002"].metalness = 0.2;
      materials["Diamond.002"].roughness = 0.0;
      materials["Diamond.002"].envMapIntensity = 2.0;
    }

    // Set gem-like properties for Ruby
    if (materials["Ruby.001"]) {
      materials["Ruby.001"].metalness = 0.3;
      materials["Ruby.001"].roughness = 0.0;
      materials["Ruby.001"].envMapIntensity = 1.5;
    }

    // Set gem-like properties for Aquamarine
    if (materials.Aquamarine) {
      materials.Aquamarine.metalness = 0.9;
      materials.Aquamarine.roughness = 0.05;
      materials.Aquamarine.envMapIntensity = 1.5;
    }
  }, [materials]);

  return (
    <group {...props} ref={ref} dispose={null}>
      <group
        position={[0, 0.922, -0.085]}
        rotation={[Math.PI / 2, 0, 0]}
        scale={0.5}
      >
        <mesh
          name="Aquamarine"
          castShadow
          receiveShadow
          geometry={nodes.Round.geometry}
          material={materials.Aquamarine}
        />
        <mesh
          name="Diamond"
          castShadow
          receiveShadow
          geometry={nodes.Round_1.geometry}
          material={materials["Diamond.002"]}
        />
        <mesh
          name="Prongs"
          castShadow
          receiveShadow
          geometry={nodes.Round_2.geometry}
          material={materials["Prongs.001"]}
        />
        <mesh
          name="Silver"
          castShadow
          receiveShadow
          geometry={nodes.Round_3.geometry}
          material={materials.Silver}
        />
        <mesh
          name="Ruby"
          castShadow
          receiveShadow
          geometry={nodes.Round_4.geometry}
          material={materials["Ruby.001"]}
        />
      </group>
    </group>
  );
});

Jewelry05.displayName = "Jewelry05";

// useGLTF.preload("/models/05.glb");
