import * as THREE from "three";
import { mergeVertices } from "three/examples/jsm/utils/BufferGeometryUtils.js";

export function splitIntoConnectedGeometries(geometry) {
  const indexedGeometry = geometry.index ? geometry : mergeVertices(geometry);

  if (!indexedGeometry.index) {
    console.warn(
      "Geometry is not indexed; splitting may not work as expected."
    );
    return [indexedGeometry];
  }

  const position = indexedGeometry.attributes.position;
  const index = indexedGeometry.index;
  const vertexCount = position.count;

  // Step 1: Create a mapping of vertices based on their coordinates
  const positionToIndex = new Map();

  for (let i = 0; i < vertexCount; i++) {
    const posKey = `${position.getX(i)},${position.getY(i)},${position.getZ(
      i
    )}`;
    if (!positionToIndex.has(posKey)) {
      positionToIndex.set(posKey, []);
    }
    positionToIndex.get(posKey).push(i);
  }

  // Step 2: Build an adjacency list based on these unique position keys
  const adjacency = new Map();

  for (let i = 0; i < index.count; i += 3) {
    const indices = [index.getX(i), index.getX(i + 1), index.getX(i + 2)].map(
      (idx) => {
        const posKey = `${position.getX(idx)},${position.getY(
          idx
        )},${position.getZ(idx)}`;
        return positionToIndex.get(posKey)[0]; // Use the first index as representative
      }
    );

    indices.forEach((v, idx) => {
      if (!adjacency.has(v)) adjacency.set(v, new Set());
      adjacency.get(v).add(indices[(idx + 1) % 3]);
      adjacency.get(v).add(indices[(idx + 2) % 3]);
    });
  }

  // Step 3: Find connected components using a graph traversal algorithm
  const visited = new Set();
  const components = [];

  positionToIndex.forEach((indices) => {
    const representative = indices[0];
    if (!visited.has(representative)) {
      const stack = [representative];
      const component = [];
      visited.add(representative);

      while (stack.length > 0) {
        const current = stack.pop();
        component.push(
          ...positionToIndex.get(
            `${position.getX(current)},${position.getY(
              current
            )},${position.getZ(current)}`
          )
        );

        adjacency.get(current).forEach((neighbor) => {
          if (!visited.has(neighbor)) {
            visited.add(neighbor);
            stack.push(neighbor);
          }
        });
      }

      components.push(component);
    }
  });

  // Step 4: Rebuild Geometries Using Individual Vertices
  const separatedGeometries = components.map((component) => {
    const newGeometry = new THREE.BufferGeometry();
    const positions = [];
    const indices = [];
    const componentIndexMap = new Map();

    component.forEach((originalIndex, newIndex) => {
      componentIndexMap.set(originalIndex, newIndex);
      positions.push(
        position.getX(originalIndex),
        position.getY(originalIndex),
        position.getZ(originalIndex)
      );
    });

    for (let i = 0; i < index.count; i += 3) {
      const triIndices = [index.getX(i), index.getX(i + 1), index.getX(i + 2)]
        .map((idx) => componentIndexMap.get(idx))
        .filter((idx) => idx !== undefined);

      if (triIndices.length === 3) {
        indices.push(triIndices[0], triIndices[1], triIndices[2]);
      }
    }

    newGeometry.setAttribute(
      "position",
      new THREE.Float32BufferAttribute(positions, 3)
    );
    if (indices.length > 0) {
      newGeometry.setIndex(indices);
    }

    newGeometry.computeBoundingSphere();
    newGeometry.computeVertexNormals();

    return newGeometry;
  });

  return separatedGeometries;
}
