//import _extends from '../node_modules/.pnpm/@react-three+drei@9.122.0_@react-three+fiber@8.18.0_@types+react@18.3.1_react-dom@18.3.1_reac_4pfq465togivmqqyv7cumdxsqe/node_modules/@babel/runtime/helpers/esm/extends.js';
import * as React from "react";
import _extends from "@babel/runtime/helpers/esm/extends";
import { useRef, useMemo } from "react";
import { extend, useThree, useFrame } from "@react-three/fiber";
import { DiamondRefraction as DiamondRefraction$1 } from "./Diamond_Fragment.js";

const isCubeTexture = (def) => def && def.isCubeTexture;
function DiamondShader({ envMap, reflectionMap, ...props }) {
  extend({
    DiamondShader: DiamondRefraction$1,
  });
  const material = useRef();
  const { size } = useThree();
  const defines = useMemo(() => {
    var _ref, _envMap$image$;
    const temp = {};
    // Sampler2D and SamplerCube need different defines
    const isCubeMap = isCubeTexture(envMap);
    const w =
      (_ref = isCubeMap
        ? (_envMap$image$ = envMap.image[0]) == null
          ? void 0
          : _envMap$image$.width
        : envMap.image.width) !== null && _ref !== void 0
        ? _ref
        : 1024;
    const cubeSize = w / 4;
    const _lodMax = Math.floor(Math.log2(cubeSize));
    const _cubeSize = Math.pow(2, _lodMax);
    const width = 3 * Math.max(_cubeSize, 16 * 7);
    const height = 4 * _cubeSize;
    if (isCubeMap) temp.ENVMAP_TYPE_CUBEM = "";
    temp.CUBEUV_TEXEL_WIDTH = `${1.0 / width}`;
    temp.CUBEUV_TEXEL_HEIGHT = `${1.0 / height}`;
    temp.CUBEUV_MAX_MIP = `${_lodMax}.0`;
    return temp;
  }, [envMap]);

  useFrame(({ camera }) => {
    material.current.viewMatrixInverse = camera.matrixWorld;
    material.current.projectionMatrixInverse = camera.projectionMatrixInverse;
  });
  return /*#__PURE__*/ React.createElement(
    "diamondShader",
    _extends(
      {
        // @ts-expect-error - diamondShader is a custom element type not recognized by TypeScript
        //key: JSON.stringify(defines)
        // @ts-expect-error - diamondShader props are dynamically extended and not fully typed
        // ,
        defines: defines,
        ref: material,
        resolution: [size.width, size.height],
        envMap: envMap,
        reflectionMap: reflectionMap,
      },
      props
    )
  );
}

export { DiamondShader };
