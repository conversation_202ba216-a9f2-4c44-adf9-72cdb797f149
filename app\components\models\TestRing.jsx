import React, { useRef, useMemo } from "react";
import {
  useGLTF,
  Decal,
  RenderTexture,
  Text,
  PerspectiveCamera,
} from "@react-three/drei";
import * as THREE from "three";
import { RGBELoader, EXRLoader } from "three-stdlib";
import { NormalCube } from "../../utils/refraction_materials/NormalCube.js";
import { useLoader, useThree } from "@react-three/fiber";
import { DiamondShader } from "../../utils/refraction_materials/Diamond_Shader.js";
import { EngravingMaterial } from "../../utils/TextBumpMapGenerator.jsx";

export function TestRing(props) {
  const groupRef = useRef();
  const { nodes } = useGLTF("/models/94118R_030ct_hh15_.glb");
  const { gl: renderer } = useThree();

  const hd = useLoader(RGBELoader, "/hdris/gem_2.hdr"); // Basically the same as the env_gem_002_30251392af.exr below, but with blue and the red and green missing.
  const hd2 = useLoader(EXRLoader, "/hdris/env_gem_002_30251392af.exr");

  // Create NormalCube instances for each diamond
  const diamondData = useMemo(() => {
    return Array.from({ length: 26 }, (_, i) => i + 1).map((index) => {
      const geometry = nodes[`diamond_round_@_${index}`].geometry;
      geometry.computeBoundingBox();
      geometry.computeBoundingSphere();
      const center = new THREE.Vector3();
      geometry.boundingBox.getCenter(center);
      const radius = geometry.boundingSphere.radius;

      // Create a NormalCube instance for this geometry
      const normalCube = new NormalCube({ renderer, geo: geometry });

      return { geometry, center, radius, normalCube };
    });
  }, [nodes, renderer]);

  // Log when clicked to help debug
  const handleClick = (e) => {
    console.log("Ring clicked:", e.object);
    e.stopPropagation();

    if (props.isPlacingDecal && props.onDecalPlacement) {
      props.onDecalPlacement(e);
      return; // Stop further processing if placing decal
    }

    // Ensure the event has the correct object property
    if (props.onClick) {
      // If clicking a child mesh, ensure the object ref is passed correctly
      if (e.object !== groupRef.current) {
        props.onClick(e);
      } else {
        // If clicking the group, create a similar event with the group as target
        const newEvent = { ...e, object: groupRef.current };
        props.onClick(newEvent);
      }
    }
  };

  // Create a unique key for the decal that changes when font or color changes
  const decalKey = useMemo(() => {
    return `${props.font}-${props.decalColor}-${props.engravingText}`;
  }, [props.font, props.decalColor, props.engravingText]);

  // Calculate dynamic font size based on text length
  const calculateFontSize = useMemo(() => {
    if (!props.engravingText) return 5;

    const textLength = props.engravingText.length;
    let baseFontSize = 5;

    // Adjust font size based on text length
    if (textLength > 12) {
      // For very long text, reduce font size more aggressively
      baseFontSize = Math.max(2.5, 5 - (textLength - 12) * 0.2);
    } else if (textLength > 8) {
      // For medium-long text, reduce font size moderately
      baseFontSize = Math.max(3.5, 5 - (textLength - 8) * 0.3);
    } else if (textLength > 6) {
      // For slightly long text, reduce font size slightly
      baseFontSize = Math.max(4, 5 - (textLength - 6) * 0.25);
    } else if (textLength < 3) {
      // For very short text, slightly increase font size
      baseFontSize = Math.min(6, 5 + (3 - textLength) * 0.3);
    }

    return baseFontSize;
  }, [props.engravingText]);

  return (
    <group
      ref={groupRef}
      {...props}
      dispose={null}
      onClick={handleClick}
      name="TestRing"
      scale={[0.084375, 0.084375, 0.084375]}
      userData={{ selectable: true }}
      position={props.position || [0, 0, 0]}
      rotation={props.rotation || [0, 0, 0]}
    >
      <mesh
        castShadow
        receiveShadow
        geometry={nodes["metal_KW_@"].geometry}
        name="Metal Band"
        onClick={handleClick}
        userData={{ selectable: true, part: "white" }}
      >
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={1.0}
          roughness={0.01}
          envMapIntensity={1.5}
        />
        {props.engravingText && (
          <Decal
            key={decalKey}
            position={props.decalPosition || [0, 1.68, 0]}
            rotation={props.decalRotation || [Math.PI / 2, 0, 0]}
            scale={props.decalScale || [6.0, 2.0, 1.5]}
            debug={props.showDecalDebug || false}
          >
            <EngravingMaterial
              text={props.engravingText}
              font={props.font}
              fontSize={calculateFontSize}
              color="#2c2c2c"
              decalColor={props.decalColor || "#000000"}
              aspect={3}
            />
          </Decal>
        )}
      </mesh>

      <mesh
        castShadow
        receiveShadow
        geometry={nodes["metal_KW_@001"].geometry}
        name="Metal KW"
        onClick={handleClick}
        userData={{ selectable: true, part: "white" }}
      >
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={1.0}
          roughness={0.01}
          envMapIntensity={1.5}
        />
      </mesh>

      <mesh
        castShadow
        receiveShadow
        geometry={nodes["metal_KW_@002"].geometry}
        name="Metal Crown"
        onClick={handleClick}
        userData={{ selectable: true, part: "white" }}
      >
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={1.0}
          roughness={0.01}
          envMapIntensity={1.5}
        />
      </mesh>

      <mesh
        castShadow
        receiveShadow
        geometry={nodes["metal_KR_@_1"].geometry}
        name="Ring_Red"
        onClick={handleClick}
        userData={{ selectable: true, part: "red" }}
      >
        <meshStandardMaterial
          color="#c0c0c0"
          metalness={1.0}
          roughness={0.01}
          envMapIntensity={1.5}
        />
      </mesh>

      {/* Diamond parts */}
      {diamondData.map((data, index) => (
        <mesh
          key={index}
          castShadow
          receiveShadow
          geometry={data.geometry}
          name={`diamond_${index + 1}`}
        >
          <DiamondShader
            color={"#ffffff"}
            aberrationStrength={0.01}
            toneMapped={false}
            envMap={hd}
            reflectionMap={hd2}
            normalEnvMap={data.normalCube.texture}
            diamondOriginCenter={data.center}
            diamondOriginRadius={data.radius}
            ior={2.3}
            bounces={3}
            fresnelstrength={0}
            fresnel={0.5}
            fresnelcolor={"#ffffff"}
          />
        </mesh>
      ))}
    </group>
  );
}

// useGLTF.preload("/models/94118R_030ct_hh15_.glb");
