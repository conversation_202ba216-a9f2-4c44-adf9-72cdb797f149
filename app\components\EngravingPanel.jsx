"use client";

import { useState, useEffect } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "./ui/accordion";

// Custom slider styles
const sliderStyles = `
  .slider-thumb {
    background: linear-gradient(45deg, #1A1D2A, #2A2D3A);
  }
  .slider-thumb::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(45deg, #60A5FA, #A78BFA);
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
  }
  .slider-thumb::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    background: linear-gradient(45deg, #3B82F6, #8B5CF6);
  }
  .slider-thumb::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(45deg, #60A5FA, #A78BFA);
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
  }
  .slider-thumb::-moz-range-thumb:hover {
    transform: scale(1.1);
    background: linear-gradient(45deg, #3B82F6, #8B5CF6);
  }
`;

export function EngravingPanel({
  onEngraveText,
  onDecalPositionChange,
  onDecalScaleChange,
  onDecalRotationChange,
  decalPosition,
  decalScale,
  decalRotation,
  decalColor,
  onDecalColorChange,
  onFontChange,
  selectedFont,
  showDebug,
  onShowDebugChange,
  isPlacingDecal,
  setIsPlacingDecal,
}) {
  const [engravingText, setEngravingText] = useState("");
  const [lineSpacing, setLineSpacing] = useState(50);
  const [wordSpacing, setWordSpacing] = useState(50);

  const fonts = [
    {
      name: "Dancing Script",
      url: "/fonts/ttf/DancingScript-Regular.ttf",
    },
    {
      name: "Homemade Apple",
      url: "/fonts/ttf/HomemadeApple-Regular.ttf",
    },
    {
      name: "Alex Brush",
      url: "/fonts/ttf/AlexBrush-Regular.ttf",
    },
    {
      name: "Roboto Serif",
      url: "/fonts/ttf/RobotoSerif-Regular.ttf",
    },
    {
      name: "Helvetiker",
      url: "/fonts/otf/Helvetica Regular.otf",
    },
  ];

  // Ensure text persists when component mounts
  useEffect(() => {
    const savedText = localStorage.getItem("engravingText");
    if (savedText) {
      setEngravingText(savedText);
      onEngraveText(savedText);
    }
  }, [onEngraveText]);

  const handleTextChange = (e) => {
    const newText = e.target.value;
    setEngravingText(newText);
    onEngraveText(newText);
  };

  const handleFontChange = (fontUrl) => {
    if (onFontChange) {
      onFontChange(fontUrl);
    }
  };

  // Add color change handler
  const handleColorChange = (e) => {
    onDecalColorChange(e.target.value);
  };

  return (
    <>
      <style jsx>{sliderStyles}</style>
      <div className="space-y-2">
        {/* Header */}
        <div className="flex items-center gap-2 mb-2">
          <div>
            <h3 className="text-base font-semibold text-white">
              Text Engraving
            </h3>
            <p className="text-xs text-gray-400">
              Add custom text to your model
            </p>
          </div>
        </div>

        {/* Text Input Section */}
        <div className="bg-gradient-to-r from-[#2A2D3A] to-[#32353F] p-3 rounded-lg border border-[#A3A3A3]/20">
          <label className="block text-xs font-medium text-gray-200 mb-1.5">
            Engraving Text
          </label>
          <div className="relative">
            <input
              type="text"
              value={engravingText}
              onChange={handleTextChange}
              className="w-full px-3 py-2 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded-lg text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              placeholder="Enter your custom text..."
              maxLength={20}
            />
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <span className="text-xs text-gray-400 bg-[#2A2D3A] px-1.5 py-0.5 rounded text-xs">
                {engravingText.length}/20
              </span>
            </div>
          </div>
        </div>

        {/* Placement Controls */}
        <div className="bg-gradient-to-r from-[#2A2D3A] to-[#32353F] p-3 rounded-lg border border-[#A3A3A3]/20">
          <div className="flex items-center gap-2 mb-2">
            <svg
              className="w-3.5 h-3.5 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <label className="text-xs font-medium text-gray-200">
              Placement Controls
            </label>
          </div>

          <div className="space-y-2">
            {/* Placement Button */}
            <button
              onClick={() => setIsPlacingDecal(!isPlacingDecal)}
              className={`w-full px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center justify-center gap-2
                ${
                  isPlacingDecal
                    ? "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg"
                    : "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg"
                }
              `}
            >
              <svg
                className="w-3.5 h-3.5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isPlacingDecal ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                )}
              </svg>
              {isPlacingDecal
                ? "Cancel Placement (ESC)"
                : "Place Text on Model"}
            </button>

            {/* Quick Fix Rotation */}
            <div>
              <label className="block text-xs font-medium text-gray-300 mb-1">
                Quick Fix Rotation
              </label>
              <div className="relative">
                <select
                  onChange={(e) => {
                    if (e.target.value === "horizontal") {
                      onDecalRotationChange([-Math.PI / 2, 0, 0]);
                    } else if (e.target.value === "vertical") {
                      onDecalRotationChange([-Math.PI / 2, 0, Math.PI / 2]);
                    }
                    e.target.value = "";
                  }}
                  className="w-full px-2.5 py-1.5 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded-lg text-xs text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none transition-all duration-200"
                  defaultValue=""
                >
                  <option value="" disabled className="bg-[#1A1D2A] text-white">
                    Fix rotation orientation...
                  </option>
                  <option
                    value="horizontal"
                    className="bg-[#1A1D2A] text-white"
                  >
                    📏 Horizontal
                  </option>
                  <option value="vertical" className="bg-[#1A1D2A] text-white">
                    📐 Vertical
                  </option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                  <svg
                    className="fill-current h-3 w-3"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Appearance Settings */}
        <div className="space-y-2">
          {/* Font Selection */}
          <div className="bg-gradient-to-r from-[#2A2D3A] to-[#32353F] p-3 rounded-lg border border-[#A3A3A3]/20">
            <div className="flex items-center gap-2 mb-2">
              <svg
                className="w-3.5 h-3.5 text-purple-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                />
              </svg>
              <label className="text-xs font-medium text-gray-200">
                Font Style
              </label>
            </div>
            <div className="relative">
              <select
                value={selectedFont}
                onChange={(e) => handleFontChange(e.target.value)}
                className="w-full px-2.5 py-1.5 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded-lg text-xs text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent appearance-none transition-all duration-200"
              >
                {fonts.map((font) => (
                  <option
                    key={font.url}
                    value={font.url}
                    className="bg-[#1A1D2A] text-white"
                  >
                    {font.name}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                <svg
                  className="fill-current h-3 w-3"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                >
                  <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Color Selection */}
          <div className="bg-gradient-to-r from-[#2A2D3A] to-[#32353F] p-3 rounded-lg border border-[#A3A3A3]/20">
            <div className="flex items-center gap-2 mb-2">
              <svg
                className="w-3.5 h-3.5 text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                />
              </svg>
              <label className="text-xs font-medium text-gray-200">
                Text Color
              </label>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <input
                  type="color"
                  value={decalColor}
                  onChange={handleColorChange}
                  className="w-8 h-8 bg-[#1A1D2A] border-2 border-[#A3A3A3]/30 rounded-lg cursor-pointer hover:border-[#A3A3A3]/50 transition-all duration-200"
                />
                <div className="absolute inset-0 rounded-lg ring-2 ring-transparent hover:ring-white/20 transition-all duration-200 pointer-events-none"></div>
              </div>
              <input
                type="text"
                value={decalColor}
                onChange={handleColorChange}
                className="flex-1 px-2.5 py-1.5 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded-lg text-xs text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                placeholder="#ffffff"
              />
            </div>
          </div>

          {/* Advanced Controls */}
          <Accordion
            type="single"
            collapsible
            className="bg-gradient-to-r from-[#2A2D3A] to-[#32353F] rounded-lg border border-[#A3A3A3]/20"
          >
            <AccordionItem value="decal" className="border-b-0">
              <AccordionTrigger className="hover:!no-underline px-3 py-2 hover:bg-white/5 rounded-t-lg transition-all duration-200">
                <div className="flex items-center gap-2">
                  <svg
                    className="w-3.5 h-3.5 text-orange-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                    />
                  </svg>
                  <span className="text-xs font-medium text-gray-200">
                    Advanced Controls
                  </span>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-3 pb-3">
                <div className="space-y-3 mt-2">
                  {/* Debug Toggle */}
                  <div className="flex items-center justify-between p-2 bg-[#1A1D2A] rounded-lg border border-[#A3A3A3]/20">
                    <div className="flex items-center gap-2">
                      <svg
                        className="w-3.5 h-3.5 text-yellow-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                        />
                      </svg>
                      <span className="text-xs text-gray-200">
                        Show Debug Info
                      </span>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={showDebug}
                        onChange={(e) => onShowDebugChange?.(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-9 h-5 bg-gray-600 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-yellow-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-yellow-500"></div>
                    </label>
                  </div>

                  {/* Position Controls */}
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-gray-200 flex items-center gap-2">
                      <svg
                        className="w-3.5 h-3.5 text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                        />
                      </svg>
                      Position
                    </h4>
                    {["X", "Y", "Z"].map((axis, index) => (
                      <div key={axis} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-400">
                            {axis}-Axis
                          </span>
                          <input
                            type="number"
                            step="0.1"
                            value={decalPosition[index].toFixed(2)}
                            onChange={(e) => {
                              const newPosition = [...decalPosition];
                              newPosition[index] = parseFloat(e.target.value);
                              onDecalPositionChange(newPosition);
                            }}
                            className="w-16 px-2 py-1 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                        </div>
                        <input
                          type="range"
                          min="-5"
                          max="5"
                          step="0.1"
                          value={decalPosition[index]}
                          onChange={(e) => {
                            const newPosition = [...decalPosition];
                            newPosition[index] = parseFloat(e.target.value);
                            onDecalPositionChange(newPosition);
                          }}
                          className="w-full h-2 bg-[#1A1D2A] rounded-lg appearance-none cursor-pointer slider-thumb"
                        />
                      </div>
                    ))}
                  </div>

                  {/* Scale Controls */}
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-gray-200 flex items-center gap-2">
                      <svg
                        className="w-3.5 h-3.5 text-green-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                        />
                      </svg>
                      Scale
                    </h4>
                    {["Width", "Height", "Depth"].map((label, index) => (
                      <div key={label} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-400">
                            {label}
                          </span>
                          <input
                            type="number"
                            step="0.1"
                            value={decalScale[index].toFixed(2)}
                            onChange={(e) => {
                              const newScale = [...decalScale];
                              newScale[index] = parseFloat(e.target.value);
                              onDecalScaleChange(newScale);
                            }}
                            className="w-16 px-2 py-1 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-green-500"
                          />
                        </div>
                        <input
                          type="range"
                          min="0.1"
                          max="10"
                          step="0.1"
                          value={decalScale[index]}
                          onChange={(e) => {
                            const newScale = [...decalScale];
                            newScale[index] = parseFloat(e.target.value);
                            onDecalScaleChange(newScale);
                          }}
                          className="w-full h-2 bg-[#1A1D2A] rounded-lg appearance-none cursor-pointer slider-thumb"
                        />
                      </div>
                    ))}
                  </div>

                  {/* Rotation Controls */}
                  <div className="space-y-3">
                    <h4 className="text-xs font-medium text-gray-200 flex items-center gap-2">
                      <svg
                        className="w-3.5 h-3.5 text-purple-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      Rotation (degrees)
                    </h4>
                    {["X", "Y", "Z"].map((axis, index) => (
                      <div key={axis} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-gray-400">
                            {axis}-Axis
                          </span>
                          <input
                            type="number"
                            step="1"
                            value={Math.round(
                              decalRotation[index] * (180 / Math.PI)
                            )}
                            onChange={(e) => {
                              const newRotation = [...decalRotation];
                              newRotation[index] =
                                parseFloat(e.target.value) * (Math.PI / 180);
                              onDecalRotationChange(newRotation);
                            }}
                            className="w-16 px-2 py-1 bg-[#1A1D2A] border border-[#A3A3A3]/30 rounded text-xs text-white focus:outline-none focus:ring-1 focus:ring-purple-500"
                          />
                        </div>
                        <input
                          type="range"
                          min="-180"
                          max="180"
                          step="1"
                          value={decalRotation[index] * (180 / Math.PI)}
                          onChange={(e) => {
                            const newRotation = [...decalRotation];
                            newRotation[index] =
                              parseFloat(e.target.value) * (Math.PI / 180);
                            onDecalRotationChange(newRotation);
                          }}
                          className="w-full h-2 bg-[#1A1D2A] rounded-lg appearance-none cursor-pointer slider-thumb"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </>
  );
}
