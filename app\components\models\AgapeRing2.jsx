import React, { useRef, useMemo } from "react";
import {
  useGLTF,
  Decal,
  RenderTexture,
  Text,
  PerspectiveCamera,
} from "@react-three/drei";
import { EngravingMaterial } from "../../utils/TextBumpMapGenerator.jsx";

export function AgapeRing2(props) {
  const groupRef = useRef();
  const { nodes, materials } = useGLTF("/models/AgapeRing2.glb");

  // Log when clicked to help debug
  const handleClick = (e) => {
    console.log("AgapeRing2 clicked:", e.object);
    e.stopPropagation();

    if (props.isPlacingDecal && props.onDecalPlacement) {
      props.onDecalPlacement(e);
      return; // Stop further processing if placing decal
    }

    // Ensure the event has the correct object property
    if (props.onClick) {
      // If clicking a child mesh, ensure the object ref is passed correctly
      if (e.object !== groupRef.current) {
        props.onClick(e);
      } else {
        // If clicking the group, create a similar event with the group as target
        const newEvent = { ...e, object: groupRef.current };
        props.onClick(newEvent);
      }
    }
  };

  // Create a unique key for the decal that changes when font or color changes
  const decalKey = useMemo(() => {
    return `${props.font}-${props.decalColor}-${props.engravingText}`;
  }, [props.font, props.decalColor, props.engravingText]);

  // Calculate dynamic font size based on text length (smaller base size for AgapeRing2)
  const calculateFontSize = useMemo(() => {
    if (!props.engravingText) return 3;

    const textLength = props.engravingText.length;
    let baseFontSize = 3;

    // Adjust font size based on text length
    if (textLength > 12) {
      // For very long text, reduce font size more aggressively
      baseFontSize = Math.max(1.5, 3 - (textLength - 12) * 0.15);
    } else if (textLength > 8) {
      // For medium-long text, reduce font size moderately
      baseFontSize = Math.max(2, 3 - (textLength - 8) * 0.2);
    } else if (textLength > 6) {
      // For slightly long text, reduce font size slightly
      baseFontSize = Math.max(2.5, 3 - (textLength - 6) * 0.15);
    } else if (textLength < 3) {
      // For very short text, slightly increase font size
      baseFontSize = Math.min(4, 3 + (3 - textLength) * 0.2);
    }

    return baseFontSize;
  }, [props.engravingText]);

  return (
    <group
      ref={groupRef}
      {...props}
      dispose={null}
      onClick={handleClick}
      name="AgapeRing2"
      userData={{ selectable: true }}
    >
      <mesh
        name="Cylinder022"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder022.geometry}
        material={materials["Material.004"]}
        position={[0, 1.542, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "gem" }}
      />
      <mesh
        name="Cylinder026"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder026.geometry}
        material={materials["Gold.001"]}
        position={[0, 1.25, 0]}
        rotation={[0, Math.PI / 6, 0]}
        scale={1.243}
        onClick={handleClick}
        userData={{ selectable: true, part: "band" }}
      >
        {props.engravingText && (
          <Decal
            key={decalKey}
            position={props.decalPosition || [0, 0, 1.2]}
            rotation={props.decalRotation || [Math.PI / 2, 0, 0]}
            scale={props.decalScale || [3.2, 1.8, 1.0]}
            debug={props.showDecalDebug || false}
          >
            <EngravingMaterial
              text={props.engravingText}
              font={props.font}
              fontSize={calculateFontSize}
              color="#2c2c2c"
              decalColor={props.decalColor || "#000000"}
              aspect={3}
            />
          </Decal>
        )}
      </mesh>
      <mesh
        name="Cylinder027"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder027.geometry}
        material={materials.Gold}
        position={[0, 1.542, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "prong" }}
      />
      <mesh
        name="Cylinder033"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder033.geometry}
        material={materials.Gold}
        onClick={handleClick}
        userData={{ selectable: true, part: "band" }}
      />
      <mesh
        name="Cylinder034"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder034.geometry}
        material={materials["Gold.001"]}
        position={[0, 1.211, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "band" }}
      />
      <mesh
        name="Cylinder035"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder035.geometry}
        material={materials["Material.005"]}
        position={[0, 1.211, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "accent" }}
      />
      <mesh
        name="Cylinder038"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder038.geometry}
        material={materials.Gold}
        position={[2.605, 0, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "band" }}
      />
      <mesh
        name="Cylinder041"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder041.geometry}
        material={materials["Material.004"]}
        position={[2.605, 1.542, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "gem" }}
      />
      <mesh
        name="Cylinder043"
        castShadow
        receiveShadow
        geometry={nodes.Cylinder043.geometry}
        material={materials.Gold}
        position={[2.605, 1.542, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "prong" }}
      />
      <mesh
        name="Plane001"
        castShadow
        receiveShadow
        geometry={nodes.Plane001.geometry}
        material={materials["Gold.001"]}
        position={[2.605, 1.098, 0]}
        onClick={handleClick}
        userData={{ selectable: true, part: "band" }}
      />
    </group>
  );
}

// useGLTF.preload("/models/AgapeRing2.glb");
