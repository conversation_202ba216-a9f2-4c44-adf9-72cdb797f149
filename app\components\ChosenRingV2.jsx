import React, { useRef, useEffect, useMemo } from "react";
import {
  useGLTF,
  useEnvironment,
  MeshRefractionMaterial,
} from "@react-three/drei";
import * as THREE from "three";

export function ChosenRingV2(props) {
  const { nodes, materials, scene } = useGLTF("/models/ChosenRing2.glb");
  const groupRef = useRef();
  const env = useEnvironment({
    files: "/hdris/diamond.hdr",
  });

  // Get all diamond meshes
  const diamondMeshes = useMemo(() => {
    const meshes = [];

    Object.entries(nodes).forEach(([key, node]) => {
      if (
        key.startsWith("metal_KW_@") &&
        key !== "metal_KW_@" &&
        key !== "metal_KW_@001" &&
        node.geometry
      ) {
        meshes.push({
          geometry: node.geometry,
          position: node.position ? node.position.toArray() : [0, 0, 0],
          rotation: node.rotation ? node.rotation.toArray() : [0, 0, 0],
          scale: node.scale ? node.scale.toArray() : [1, 1, 1],
          name: key,
        });
      }
    });

    console.log("Found diamond meshes:", meshes.length);
    return meshes;
  }, [nodes]);

  // Add support for the onLoad handler to provide scene data for stats
  useEffect(() => {
    if (props.onLoad && scene) {
      // Ensure all meshes have proper names and are selectable before passing to onLoad
      scene.traverse((child) => {
        if (child.isMesh) {
          // Ensure geometry attributes exist
          if (
            !child.geometry ||
            !child.geometry.attributes ||
            !child.geometry.attributes.position
          ) {
            console.warn(
              `Mesh ${child.name || child.uuid} has invalid geometry`
            );
            return;
          }

          child.castShadow = true;
          child.receiveShadow = true;
          child.userData = { selectable: true };

          // Set proper names for the main meshes
          if (child.geometry === nodes["metal_KW_@"].geometry) {
            child.name = "Ring_Red";
            child.userData.part = "red";
          } else if (child.geometry === nodes["metal_KW_@001"].geometry) {
            child.name = "Ring_White";
            child.userData.part = "white";
          } else if (!child.name) {
            child.name = `Ring_Part_${child.id}`;
          }

          child.onClick = props.onClick;
        }
      });

      props.onLoad(scene);
    }
  }, [scene, props.onLoad, props.onClick, nodes]);

  // Log when clicked to help debug
  const handleClick = (e) => {
    console.log("Ring clicked:", e.object);
    e.stopPropagation();

    // Ensure the event has the correct object property
    if (props.onClick) {
      // If clicking a child mesh, ensure the object ref is passed correctly
      if (e.object !== groupRef.current) {
        props.onClick(e);
      } else {
        // If clicking the group, create a similar event with the group as target
        const newEvent = { ...e, object: groupRef.current };
        props.onClick(newEvent);
      }
    }
  };

  // Helper function to create mesh with proper geometry checks
  const createMesh = (node, name, part, materialProps) => {
    if (
      !node ||
      !node.geometry ||
      !node.geometry.attributes ||
      !node.geometry.attributes.position
    ) {
      console.warn(`Invalid geometry for ${name}`);
      return null;
    }

    return (
      <mesh
        castShadow
        receiveShadow
        geometry={node.geometry}
        name={name}
        onClick={handleClick}
        userData={{ selectable: true, part }}
      >
        <meshStandardMaterial {...materialProps} />
      </mesh>
    );
  };

  // Ensure diamonds have proper normals
  useEffect(() => {
    diamondMeshes.forEach((mesh) => {
      if (mesh.geometry) {
        // Compute vertex normals if they don't exist
        if (!mesh.geometry.attributes.normal) {
          mesh.geometry.computeVertexNormals();
        }
      }
    });
  }, [diamondMeshes]);

  return (
    <group
      ref={groupRef}
      {...props}
      dispose={null}
      onClick={handleClick}
      name="ChosenRing"
      scale={[0.084375, 0.084375, 0.084375]}
      userData={{ selectable: true }}
      position={props.position || [0, 0, 0]}
      rotation={props.rotation || [0, 0, 0]}
    >
      {createMesh(nodes["metal_KW_@"], "Ring_Red", "red", {
        color: "#ffcbad",
        metalness: 1.0,
        roughness: 0.01,
        envMapIntensity: 1.5,
      })}

      {createMesh(nodes["metal_KW_@001"], "Ring_White", "white", {
        color: "#c0c0c0",
        metalness: 1.0,
        roughness: 0.01,
        envMapIntensity: 1.5,
      })}

      {diamondMeshes.map((mesh, index) => (
        <mesh
          key={mesh.name}
          geometry={mesh.geometry}
          position={mesh.position}
          rotation={mesh.rotation}
          scale={mesh.scale}
          castShadow
          receiveShadow
        >
          <MeshRefractionMaterial
            envMap={env}
            toneMapped={false}
            bounces={3}
            ior={2.75}
            fresnel={1}
            color="white"
            fastChroma
            aberrationStrength={0.02}
            transmissionSampler
          />
        </mesh>
      ))}
    </group>
  );
}

useGLTF.preload("/models/ChosenRing2.glb");
