import React, { useEffect } from "react";
import { useGLTF } from "@react-three/drei";

export function PrototypeA({ position, wireframe, onLoad }) {
  const { scene } = useGLTF("/models/PrototypeA2.glb");

  useEffect(() => {
    scene.traverse((child) => {
      if (child.isMesh) {
        child.material.wireframe = wireframe;
      }
    });
    onLoad?.(scene);
  }, [scene, wireframe, onLoad]);

  return <primitive object={scene} position={position} />;
}

// useGLTF.preload("/models/PrototypeA2.glb");
