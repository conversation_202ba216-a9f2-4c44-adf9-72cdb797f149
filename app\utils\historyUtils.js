import * as THREE from "three";

export const createHistoryState = (selectedObjects, lights, lightRefs) => {
  return {
    selectedObjects: selectedObjects.map((obj) => ({
      id: obj.uuid,
      position: obj.position.clone(),
      rotation: obj.rotation.clone(),
      scale: obj.scale.clone(),
      material: obj.material
        ? {
            color: obj.material.color ? obj.material.color.getHex() : 0xffffff,
            roughness: obj.material.roughness,
            metalness: obj.material.metalness,
            wireframe: obj.material.wireframe,
            transparent: obj.material.transparent,
            opacity: obj.material.opacity,
          }
        : null,
    })),
    lights: lights.map((light) => ({
      ...light,
      position: lightRefs[light.id]
        ? new THREE.Vector3().copy(lightRefs[light.id].position)
        : new THREE.Vector3(
            light.position.x,
            light.position.y,
            light.position.z
          ),
    })),
  };
};

export const applyHistoryState = (state, sceneObjects, lightRefs) => {
  // Apply object transformations
  state.selectedObjects.forEach((savedObj) => {
    const obj = sceneObjects.find((o) => o.uuid === savedObj.id);
    if (obj) {
      obj.position.copy(savedObj.position);
      obj.rotation.copy(savedObj.rotation);
      obj.scale.copy(savedObj.scale);

      if (savedObj.material && obj.material) {
        obj.material.color.setHex(savedObj.material.color);
        obj.material.roughness = savedObj.material.roughness;
        obj.material.metalness = savedObj.material.metalness;
        obj.material.wireframe = savedObj.material.wireframe;
        obj.material.transparent = savedObj.material.transparent;
        obj.material.opacity = savedObj.material.opacity;
        obj.material.needsUpdate = true;
      }
    }
  });

  // Apply light positions
  state.lights.forEach((savedLight) => {
    const lightRef = lightRefs[savedLight.id];
    if (lightRef) {
      lightRef.position.copy(savedLight.position);
    }
  });
};
